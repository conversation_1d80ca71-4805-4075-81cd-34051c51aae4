<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.SceneryMapper">

    <select id="selectAll" resultType="com.example.entity.Scenery">
        select scenery.*,province.name as provinceName from `scenery`
        left join province on scenery.province_id = province.id
        <where>
            <if test="name != null"> and scenery.name like concat('%', #{name}, '%')</if>
            <if test="provinceId != null"> and scenery.province_id=#{provinceId}</if>
        </where>
        order by id
    </select>

    <delete id="deleteById">
        delete from `scenery`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Scenery" useGeneratedKeys="true">
        insert into `scenery`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="content != null">content,</if>
            <if test="description != null">description,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="recommend != null">recommend,</if>
            <if test="views != null">views,</if>
            <if test="img != null">img,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="content != null">#{content},</if>
            <if test="description != null">#{description},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="views != null">#{views},</if>
            <if test="img != null">#{img},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Scenery">
        update `scenery`
        <set>
            <if test="name != null">
               name = #{name},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="img != null">
               img = #{img},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId},
            </if>
            <if test="recommend != null">
                recommend = #{recommend},
            </if>
            <if test="views != null">
                views = #{views},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>