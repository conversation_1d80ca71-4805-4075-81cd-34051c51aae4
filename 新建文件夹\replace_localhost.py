#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL文件localhost替换脚本
将SQL文件中的localhost替换为**********
"""

import os
import re
import shutil
from datetime import datetime


def replace_localhost_in_sql(input_file, output_file=None, target_ip="**********"):
    """
    替换SQL文件中的localhost为指定IP地址
    
    Args:
        input_file (str): 输入的SQL文件路径
        output_file (str): 输出的SQL文件路径，如果为None则覆盖原文件
        target_ip (str): 目标IP地址，默认为**********
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在！")
        return False
    
    # 如果没有指定输出文件，则覆盖原文件
    if output_file is None:
        # 创建备份文件
        backup_file = f"{input_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(input_file, backup_file)
        print(f"已创建备份文件：{backup_file}")
        output_file = input_file
    
    try:
        # 读取原文件内容
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计替换前的localhost数量
        localhost_count = content.count('localhost')
        print(f"发现 {localhost_count} 个 localhost 需要替换")
        
        if localhost_count == 0:
            print("文件中没有找到localhost，无需替换")
            return True
        
        # 执行替换
        # 使用正则表达式确保只替换完整的localhost单词
        new_content = re.sub(r'\blocalhost\b', target_ip, content)
        
        # 验证替换结果
        new_localhost_count = new_content.count('localhost')
        replaced_count = localhost_count - new_localhost_count
        
        # 写入新文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"替换完成！")
        print(f"- 成功替换了 {replaced_count} 个 localhost")
        print(f"- 剩余 {new_localhost_count} 个 localhost（可能在注释或字符串中）")
        print(f"- 输出文件：{output_file}")
        
        return True
        
    except Exception as e:
        print(f"处理文件时发生错误：{str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("SQL文件localhost替换工具")
    print("=" * 50)
    
    # 默认处理当前目录下的hotelsystem.sql文件
    input_file = "hotelsystem.sql"
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"未找到默认文件 {input_file}")
        # 让用户输入文件路径
        input_file = input("请输入SQL文件路径: ").strip()
        if not input_file:
            print("未输入文件路径，程序退出")
            return
    
    # 询问用户是否要自定义IP地址
    target_ip = input("请输入目标IP地址（直接回车使用默认值 **********）: ").strip()
    if not target_ip:
        target_ip = "**********"
    
    # 询问用户是否要指定输出文件
    output_choice = input("是否指定输出文件？(y/n，默认覆盖原文件): ").strip().lower()
    output_file = None
    
    if output_choice in ['y', 'yes']:
        output_file = input("请输入输出文件路径: ").strip()
        if not output_file:
            print("未输入输出文件路径，将覆盖原文件")
            output_file = None
    
    # 执行替换
    print(f"\n开始处理文件：{input_file}")
    print(f"目标IP地址：{target_ip}")
    
    success = replace_localhost_in_sql(input_file, output_file, target_ip)
    
    if success:
        print("\n✅ 处理完成！")
    else:
        print("\n❌ 处理失败！")


if __name__ == "__main__":
    main()
