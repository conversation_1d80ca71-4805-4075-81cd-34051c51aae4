package com.example.controller;

import com.example.common.Result;
import com.example.entity.Scenery;
import com.example.service.SceneryService;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 前端请求接口
 */
@RestController
@RequestMapping("/scenery")
public class SceneryController {

    @Resource
    private SceneryService sceneryService;

    /**
     * 新增
     */
    @PostMapping("/add")
    public Result add(@RequestBody Scenery scenery) {
        sceneryService.add(scenery);
        return Result.success();
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    public Result update(@RequestBody Scenery scenery) {
        sceneryService.updateById(scenery);
        return Result.success();
    }

    /**
     * 单个删除
     */
    @DeleteMapping("/delete/{id}")
    public Result delete(@PathVariable Integer id) {
        sceneryService.deleteById(id);
        return Result.success();
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/delete/batch")
    public Result delete(@RequestBody List<Integer> ids) {
        sceneryService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 单个查询
     */
    @GetMapping("/selectById/{id}")
    public Result selectById(@PathVariable Integer id) {
        Scenery scenery = sceneryService.selectById(id);
        return Result.success(scenery);
    }

    /**
     * 查询所有
     */
    @GetMapping("/selectAll")
    public Result selectAll(Scenery scenery) {
        List<Scenery> list = sceneryService.selectAll(scenery);
        return Result.success(list);
    }

    /**
     * 分页查询
     */
    @GetMapping("/selectPage")
    public Result selectPage(Scenery scenery,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Scenery> pageInfo = sceneryService.selectPage(scenery, pageNum, pageSize);
        return Result.success(pageInfo);
    }

}
