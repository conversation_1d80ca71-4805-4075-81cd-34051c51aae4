<template>
  <div  style="width:80%;margin:20px auto;min-height:600px">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in data.productData" style="margin-bottom:20px">
          <div class="front_card">
            <img :src="item.img" alt="" style="width:100%;height:220px;border-radius:5px 5px 0 0">
            <div style="font-size:16px;font-weight:bold;text-align:center;margin-top:5px">{{item.name}}</div>
            <div style="padding:10px">
              <div class="line2" style="color:#666666;text-align:justify">{{item.description}}</div>
              <div style="margin-top:5px;color:#989898">剩余：{{item.num}}{{item.unit}}</div>
              <div style="display:flex;align-items:center;margin-top:5px">
                <div style="color:red;font-weight:bold;flex:1">￥{{item.price}} / {{item.unit}}</div>
                <div style="width:100px">
                  <el-button type="info" @click="buyInit(item)">立即购买</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    <div v-if="data.total">
      <el-pagination @current-change="loadProduct" background layout="prev, pager, next" :page-size="data.pageSize" v-model:current-page="data.pageNum" :total="data.total" />
    </div>
    <el-dialog title="特产信息" v-model="data.formVisible" width="40%" destroy-on-close>
      <el-form ref="formRef" :rules="data.rules" :model="data.form" label-width="100px" style="padding: 20px">
        <el-form-item prop="num" label="特产数量">
          <el-input-number v-model="data.form.num" :min="1" :max="data.form.max"></el-input-number>
        </el-form-item>
        <el-form-item prop="name" label="收货人姓名">
          <el-input  v-model="data.form.name" placeholder="请输入收货人姓名"></el-input>
        </el-form-item>
        <el-form-item prop="phone" label="收货人电话">
          <el-input  v-model="data.form.phone" placeholder="请输入收货人电话"></el-input>
        </el-form-item>
        <el-form-item prop="address" label="收货人地址">
          <el-input type="textarea" :rows="4" v-model="data.form.address" placeholder="请输入收货人地址"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="buy">下 单</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import {reactive,ref} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";

const formRef = ref()
const data = reactive({
  total:0,
  pageNum:1,
  pageSize:8,
  productData:[],
  formVisible:false,
  rules:{
    num: [
      { required: true, message: '请输入购买数量', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '请输入收货人姓名', trigger: 'blur' }
    ],
    phone: [
      { required: true, message: '请输入收货人电话', trigger: 'blur' }
    ],
    address: [
      { required: true, message: '请输入收货人地址', trigger: 'blur' }
    ],
  }
})
const buyInit = (product) =>{
  data.form = {}
  data.form.productId = product.id
  data.form.num = 1
  data.form.max = product.num
  data.formVisible = true
}
const buy = () =>{
  formRef.value.validate(valid =>{
    if(valid){
      request.post('productOrders/add',data.form).then(res =>{
        if(res.code === '200'){
          ElMessage.success('下单成功，请到支付页面进行支付')
          loadProduct()
          data.formVisible = false
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}
const loadProduct =()=>{
  request.get('/product/selectPage',{
    params:{
      pageNum:data.pageNum,
      pageSize:data.pageSize,
    }
  }).then(res =>{
    if(res.code === '200'){
      data.productData =res.data?.list
      data.total = res.data.total
    } else{
      ElMessage.error(res.msg)
    }
  })
  }
loadProduct()
</script>