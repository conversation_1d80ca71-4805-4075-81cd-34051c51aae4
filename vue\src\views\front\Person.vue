<template>
  <div  style="width: 40%; margin: 5px auto; min-height:600px ">
  <div class="card">
    <el-form ref="user" :model="data.user" label-width="80px" style="padding: 20px">
      <div style="text-align: center; margin-bottom: 20px">
        <el-upload
            :action="baseUrl + '/files/upload'"
            :on-success="handleFileUpload"
            :show-file-list="false"
            class="avatar-uploader"
        >
          <img v-if="data.user.avatar" :src="data.user.avatar" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </div>
      <el-form-item prop="username" label="用户名">
        <el-input disabled v-model="data.user.username" placeholder="请输入用户名"></el-input>
      </el-form-item>
      <el-form-item prop="name" label="姓名">
        <el-input v-model="data.user.name" placeholder="请输入姓名"></el-input>
      </el-form-item>
      <el-form-item prop="phone" label="电话">
        <el-input v-model="data.user.phone" placeholder="请输入电话"></el-input>
      </el-form-item>
      <el-form-item prop="email" label="邮箱">
        <el-input v-model="data.user.email" placeholder="请输入邮箱"></el-input>
      </el-form-item>
      <el-form-item prop="email" label="账户余额">
        <span style="color:red">￥{{data.user.account}}</span>
      </el-form-item>
      <div style="text-align: center">
        <el-button type="warning" @click="recharge">充 值</el-button>
        <el-button type="primary" @click="update">保 存</el-button>
      </div>
    </el-form>
  </div>
    <el-dialog title="充值中心" v-model="data.formVisible" width="40%" destroy-on-close>
      <el-form ref="form"  label-width="70px" style="padding: 20px">
        <el-form-item prop="type" label="充值方式">
          <el-radio-group v-model="data.type" fill="#A3A6AD" >
            <el-radio-button label="支付宝" value="aliPay" />
            <el-radio-button label="微信" value="wePay" />
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="account" label="金额">
          <el-input v-model="data.account" placeholder="请输入充值金额"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="rechargeLine">充 值</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive } from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";

const baseUrl = import.meta.env.VITE_BASE_URL

const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
  account:0,
  type:'aliPay',
  formVisible:false,
})
const loadPerson = () =>{
  request.get('/user/selectById/' + data.user.id).then(res =>{
    if (res.code === '200'){
      data.user =res.data
      localStorage.setItem('xm-user', JSON.stringify(data.user))
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadPerson()
const handleFileUpload = (res) => {
  data.user.avatar = res.data
}

const emit = defineEmits(['updateUser'])
const update = () => {
  if (data.user.role === 'USER') {
    request.put('/user/update', data.user).then(res => {
      if (res.code === '200') {
        ElMessage.success('保存成功')
        localStorage.setItem('xm-user', JSON.stringify(data.user))
        emit('updateUser')
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
const recharge = () => {
  data.account = 100
  data.formVisible = true
}
const rechargeLine = () =>{
  if(!data.account || data.account <= 0){
    ElMessage.warning('请输入正确的金额')
     return
  }
  request.get('/user/recharge/' + data.account).then(res =>{
    if(res.code === '200'){
      ElMessage.success('充值成功')
      localStorage.setItem('xm-user', JSON.stringify(res.data))
      data.user = res.data
      data.formVisible = false
    }
  })
}
</script>

<style>
.avatar-uploader {
  height: 120px;
}
.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}
</style>