<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.ProvinceMapper">

    <select id="selectAll" resultType="com.example.entity.Province">
        select * from `province`
        <where>
            <if test="name != null"> and name like concat('%', #{name}, '%')</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `province`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Province" useGeneratedKeys="true">
        insert into `province`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
        </trim>
    </insert>
    <update id="updateById" parameterType="com.example.entity.Province">
        update `province`
        <set>
            <if test="name != null">
                name = #{title},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>