<template>
  <div >
    <!--<div class="front-notice"><el-icon><Bell /></el-icon>公告：{{ data.top }}</div>-->
    <div class="front-header" >
      <div class="front-header-left">
        <img src="@/assets/imgs/logo.png" alt="">
        <div class="title">宾馆预订</div>
      </div>
      <div class="front-header-center"  >
        <el-menu :default-active="router.currentRoute.value.path" router mode="horizontal">
          <el-menu-item index="/front/home">首页</el-menu-item>
          <el-menu-item index="/front/person">个人中心</el-menu-item>
          <el-menu-item index="/front/feedback">反馈中心</el-menu-item>
          <el-menu-item index="/front/notice">系统公告</el-menu-item>
          <el-menu-item index="/front/scenery">热门景点</el-menu-item>
          <el-menu-item index="/front/business">相关宾馆</el-menu-item>
          <el-menu-item index="/front/article">旅游论坛</el-menu-item>
          <el-menu-item index="/front/product">省份特产</el-menu-item>
        </el-menu>
      </div>
      <div class="front-header-right">
        <div v-if="!data.user.id">
          <el-button @click="router.push('/login')">登录</el-button>
          <el-button @click="router.push('/register')">注册</el-button>
        </div>
        <div v-else>
          <el-dropdown style="cursor: pointer; height: 60px">
            <div style="display: flex; align-items: center">
              <img style="width: 40px; height: 40px; border-radius: 50%;" :src="data.user.avatar" alt="">
              <span style="margin-left: 5px; color:white">{{ data.user.name }}</span><el-icon><arrow-down /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="router.push('/front/myFeedback')">我的反馈</el-dropdown-item>
                <el-dropdown-item @click="router.push('/front/myArticle')">我的帖子</el-dropdown-item>
                <el-dropdown-item @click="router.push('/front/myCollect')">我的收藏</el-dropdown-item>
                <el-dropdown-item @click="router.push('/front/productOrders')">特产订单</el-dropdown-item>
                <el-dropdown-item @click="router.push('/front/businessOrders')">宾馆订单</el-dropdown-item>
                <el-dropdown-item @click="router.push('/front/password')">修改密码</el-dropdown-item>
                <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
    <div class="main-body">
      <RouterView @updateUser="updateUser" />
      <div style="background-color: #3c3c3c;height: 350px;margin-top: 50px">
        <div style="width: 50%;margin: 0 auto;display: flex;color: #c2c2c2">
          <div style="flex: 1;margin-top: 20px">
            <div style="padding: 10px 0">旅家民宿预订网</div>
            <div style="padding: 5px 0">上亿旅行者共同打造的"旅行神器"</div>
            <div style="padding: 5px 0">60,000 多个全球旅游目的地</div>
            <div style="padding: 5px 0">600,000 个细分目的地新玩法</div>
            <div style="padding: 5px 0">760,000,000 次攻略下载</div>
            <div style="padding: 5px 0">38,000 家旅游产品供应商</div>
          </div>
          <div style="flex: 1;margin-top: 20px">
            <div style="padding: 10px 0">关于我们</div>
            <div style="padding: 5px 0">关于宾馆预订联系我们</div>
            <div style="padding: 5px 0">隐私政策商标声明</div>
            <div style="padding: 5px 0">服务协议</div>
            <div style="padding: 5px 0">商城平台服务协议</div>
            <div style="padding: 5px 0">网络信息侵权通知指引</div>
            <div style="padding: 5px 0">民宿旅游网服务监督员</div>
            <div style="padding: 5px 0">网站地图加入民宿网站</div>
          </div>
          <div style="flex: 1;margin-top: 20px">
            <div style="padding: 10px 0;">宾馆服务</div>
            <div style="display: flex">
              <div style="flex: 1;padding: 10px 0">旅游攻略</div>
              <div style="flex: 1;padding: 5px 0">宾馆预订</div>
            </div>
            <div style="display: flex">
              <div style="flex: 1;padding: 10px 0">旅游特价</div>
              <div style="flex: 1;padding: 5px 0">旅游问答</div>
            </div>
            <div style="display: flex">
              <div style="flex: 1;padding: 10px 0">旅游指南</div>
              <div style="flex: 1;padding: 5px 0">旅游资讯</div>
            </div>
            <div style="display: flex">
              <div style="flex: 1;padding: 10px 0">APP下载</div>

            </div>
            <div style="padding: 5px 0">旅行商城全球商家入驻</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import router from "@/router/index.js";
  import { reactive } from "vue";
  import request from "@/utils/request.js";

  const data = reactive({
    user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
    top: '',
    noticeData: []
  })

  const logout = () => {
    localStorage.removeItem('xm-user')
    router.push('/login')
  }

  const updateUser = () => {
    data.user =  JSON.parse(localStorage.getItem('xm-user') || '{}')
  }

  const loadNotice = () => {
    request.get('/notice/selectAll').then(res => {
      data.noticeData = res.data
      let i = 0
      if (data.noticeData && data.noticeData.length) {
        data.top = data.noticeData[0].content
        setInterval(() => {
          data.top = data.noticeData[i].content
          i++
          if (i === data.noticeData.length) {
            i = 0
          }
        }, 2500)
      }
    })
  }
  loadNotice()
</script>

<style scoped>
@import "@/assets/css/front.css";
</style>