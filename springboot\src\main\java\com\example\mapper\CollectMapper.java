package com.example.mapper;

import com.example.entity.Collect;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CollectMapper {

    int insert(Collect collect);

    void updateById(Collect collect);

    void deleteById(Integer id);

    @Select("select * from `collect` where id = #{id}")
    Collect selectById(Integer id);

    List<Collect> selectAll(Collect collect);
    @Select("select * from collect where type_id = #{id}")
    List<Collect> selectByTypeId(Integer id);
    @Delete("delete from collect where user_id = #{userId} and type_id = #{typeId}")
    void deleteByUserId(@Param("userId") Integer userId,@Param("typeId") Integer typeId);
}
