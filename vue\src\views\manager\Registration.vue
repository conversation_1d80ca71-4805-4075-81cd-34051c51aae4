<template>
  <div>
    <div class="card" style="margin-bottom: 5px">
      <el-input v-model="data.orderNo" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输入订单编号查询"></el-input>
      <el-button type="info" plain @click="load">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div class="card" style="margin-bottom: 5px">
      <el-table stripe :data="data.registrationData">
        <el-table-column prop="orderNo" label="订单号" show-overflow-tooltip />
        <el-table-column prop="userName" label="下单用户" width="120"  />
        <el-table-column prop="businessName" label="宾馆名称" width="120" />
        <el-table-column prop="typeName" label="房间类型" width="120" />
        <el-table-column prop="roomName" label="房间编号" width="120" />
        <el-table-column prop="inTime" label="登记时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="outTime" label="退房时间" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template v-slot="scope">
            <el-button  :disabled="scope.row.outTime" type="info"  @click="outRoom(scope.row)">退房</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="card" v-if="data.total">
      <el-pagination @current-change="load" background layout="prev, pager, next" :page-size="data.pageSize" v-model:current-page="data.pageNum" :total="data.total" />
    </div>
  </div>
</template>

<script setup>

import {reactive,ref} from "vue";
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";

const formRef = ref()
const data = reactive({
  orderNo:null,
  status:null,
  total:0,
  pageNum:1,
  pageSize:5,
  registrationData:[],
  form:{},
})
const outRoom = (row) =>{
  ElMessageBox.confirm('是否确认退房？','退房确认',{type:'warning'}).then(res =>{
    data.form = JSON.parse(JSON.stringify(row))
    update()
  }).catch(err =>{
    console.error(err)
  })
}
const load = () => {
  request.get('/registration/selectPage', {
    params: {
      pageNum: data.pageNum,
      pageSize: data.pageSize,
      orderNo:data.orderNo,
    }
  }).then(res => {
    if (res.code === '200') {
      data.registrationData = res.data?.list || []
      data.total = res.data?.total
    }
  })
}


const update = () => {
  request.put('/registration/update', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    }
  })
}

const del = (id) => {
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete('/registration/delete/' + id).then(res => {
      if (res.code === '200') {
        ElMessage.success("删除成功")
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const delBatch = () => {
  if (!data.ids.length) {
    ElMessage.warning("请选择数据")
    return
  }
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete("/registration/delete/batch", {data: data.ids}).then(res => {
      if (res.code === '200') {
        ElMessage.success('操作成功')
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}

const reset = () => {
  data.orderNo = null
  load()
}

load()
</script>