package com.example.service;

import cn.hutool.core.util.ObjectUtil;
import com.example.entity.Province;
import com.example.entity.Product;
import com.example.mapper.ProvinceMapper;
import com.example.mapper.ProductMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层方法
 */
@Service
public class ProductService {

    @Resource
    private ProductMapper productMapper;
    @Resource
    private ProvinceMapper provinceMapper;

    public void add(Product product) {
        productMapper.insert(product);
    }

    public void updateById(Product product) {
        productMapper.updateById(product);
    }

    public void deleteById(Integer id) {
        productMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            productMapper.deleteById(id);
        }
    }

    public Product selectById(Integer id) {

         Product product = productMapper.selectById(id);
         Province province = provinceMapper.selectById(product.getProvinceId());
        if (ObjectUtil.isNotEmpty(province)){
            product.setProvinceName(province.getName());
        }
        return product;
    }

    public List<Product> selectAll(Product product) {
        return productMapper.selectAll(product);
    }

    public PageInfo<Product> selectPage(Product product, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Product> list = productMapper.selectAll(product);
        return PageInfo.of(list);
    }

}
