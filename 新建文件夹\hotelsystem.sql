/*
 Navicat Premium Data Transfer

 Source Server         : yang_666
 Source Server Type    : MySQL
 Source Server Version : 80032
 Source Host           : 127.0.0.1:3306
 Source Schema         : hotelsystem

 Target Server Type    : MySQL
 Target Server Version : 80032
 File Encoding         : 65001

 Date: 01/08/2025 20:23:30
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '账号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '姓名',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES (1, 'admin', 'admin', 'Gypsophila。', 'http://**********:9090/files/download/1751340484957-微信图片_20250701105924_16.jpg', 'ADMIN', '18899990011', '<EMAIL>');
INSERT INTO `admin` VALUES (2, '111', '123456', '111', 'http://**********:9090/files/download/1751338679196-微信图片_20250701105924_16.jpg', 'ADMIN', '***********11', '<EMAIL>');
INSERT INTO `admin` VALUES (3, '222', '123456', '黑大帅', 'http://**********:9090/files/download/1751604769173-黑大帅.jpg', 'ADMIN', '2222222', '<EMAIL>');

-- ----------------------------
-- Table structure for article
-- ----------------------------
DROP TABLE IF EXISTS `article`;
CREATE TABLE `article`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `views` int NULL DEFAULT 0,
  `user_id` int NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of article
-- ----------------------------
INSERT INTO `article` VALUES (1, '🌿「西湖梦寻」：一湖烟雨半城诗，邂逅江南最温柔的四季', 'http://**********:9090/files/download/1752894937204-西湖.jpg', '<p style=\"text-align: start;\">你是否想过，把日子过成一首诗？<br>来西湖吧——<br>这里的水波潋滟着唐宋的月光，<br>堤岸垂柳轻扫过苏东坡的词章。</p><p style=\"text-align: start;\">🌸 <strong>春</strong> 在苏堤醒来<br>「苏堤春晓」的晨雾里，<br>一株桃红倚着柳绿，<br>像是谁不小心打翻了水彩盘。<br>（📌 清晨6点人最少，独享水墨画卷）</p><p style=\"text-align: start;\">🍃 <strong>夏</strong> 坠入曲院风荷<br>摇橹船穿过桥洞，<br>惊起一滩鸥鹭，<br>荷叶托住坠落的蝉鸣，<br>船娘吴语软糯：“前面就是三潭印月呀~”<br>（🚣‍♀️ 推荐茅家埠-乌龟潭小众航线）</p><p style=\"text-align: start;\">🍁 <strong>秋</strong> 醉在满陇桂雨<br>白墙黛瓦的茶舍边，<br>金桂簌簌落进龙井，<br>空气甜得能掐出蜜来。<br>（🍵 虎跑泉配桂花糕才是正确打开方式）</p><p style=\"text-align: start;\">❄️ <strong>冬</strong> 邂逅断桥残雪<br>当飞雪轻吻湖面，<br>雷峰塔披上素纱，<br>恍惚能听见白娘子在雪中低语：<br>“原来千年，不过一场等。”</p><p style=\"text-align: start;\">✨ <strong>私藏时刻</strong></p><ul><li style=\"text-align: start;\">宝石山巅等一场紫粉色日出</li><li style=\"text-align: start;\">西泠印社的黄昏看孤山变金</li><li style=\"text-align: start;\">夜游西湖时，邂逅浮在水面的月亮灯</li></ul><blockquote style=\"text-align: start;\">“未能抛得杭州去，一半勾留是此湖”你准备把心留在西湖的哪一页？</blockquote>', '2025-07-19 11:17:16', 43, 4, '通过');
INSERT INTO `article` VALUES (3, '🌄「泰山记」：与历代帝王共赴一场云端朝圣', 'http://**********:9090/files/download/1752895673952-泰山.jpg', '<p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1752895759054-泰山1.jpg\" alt=\"\" data-href=\"\" style=\"width: 472.32px;height: 291.05px;\"></p><p style=\"text-align: start;\">⛰️ <strong>天阶六千级，一步一重天</strong><br>从红门到玉皇顶的6666级台阶上<br>刻着秦皇汉武的封禅印记<br>也留着李白杜甫的跌宕诗行<br>挑山工的扁担吱呀声里<br>藏着比十八盘更陡峭的人间</p><p style=\"text-align: start;\">🌅 <strong>在日观峰，接住第一缕华夏晨光</strong><br>当云海翻涌成滚烫的金汤<br>才懂为何司马迁说：<br>\"人固有一死，或重于泰山\"<br>（📌 夜爬建议：22点出发，5点前占观日崖C位）</p><p style=\"text-align: start;\">📜 <strong>摩崖石刻，一部露天的中国史</strong><br>唐玄宗的《纪泰山铭》灿若星辰<br>\"五岳独尊\"的朱砂红穿透千年<br>每一笔都是帝王将相的野心<br>也是樵夫挑夫的圣经</p><p style=\"text-align: start;\">☁️ <strong>南天门以上的神仙日子</strong><br>天街的云雾豆腐脑冒着仙气<br>碧霞祠的香火牵着众生愿望<br>若遇雨后初晴<br>脚下云浪翻涌如《千里江山图》活了过来</p><p style=\"text-align: start;\"><strong>✨ 朝圣者须知</strong></p><ul><li style=\"text-align: start;\">夜爬备好手电筒，中天门有补给站</li><li style=\"text-align: start;\">岱庙必看：宋天贶殿壁画《泰山神启跸回銮图》</li><li style=\"text-align: start;\">隐藏玩法：后石坞古松奇石，少有人至</li></ul><blockquote style=\"text-align: start;\">\"泰山不是山，是立在天地间的一柄量人尺\"你，敢来丈量自己的魂魄吗？<br></blockquote>', '2025-07-19 11:29:22', 11, 1, '通过');
INSERT INTO `article` VALUES (4, '山海关游记', 'http://**********:9090/files/download/1752919501219-山海关3.jpg', '<p style=\"text-align: start;\">🌊 <strong>老龙头：长城在此饮海</strong><br>当斑驳城墙探入渤海<br>浪花溅起六百年前的烽烟<br>站在澄海楼远眺<br>才懂什么叫\"长城连海水连天\"<br>（📌 涨潮时最佳，看惊涛拍打城墙基）</p><p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1752918491592-山海关3.jpg\" alt=\"\" data-href=\"\" style=\"width: 558.32px;height: 372.40px;\"></p><p style=\"text-align: start;\">🏮 <strong>天下第一关：一匾镇山河</strong><br>抚过瓮城冰凉的青砖<br>城门上\"天下第一关\"的朱砂未干<br>恍惚听见1644年的马蹄声<br>——那一夜，吴三桂推开城门<br>改写了整个中国的晨昏</p><p style=\"text-align: start;\"><strong>📜 古城慢行指南</strong><br>• 登城楼寻找箭垛上的战争记忆<br>• 角山长城徒步，触摸最原汁原味的野长城<br>• 孟姜女庙前，听潮声哭倒八百里的传说</p><p style=\"text-align: start;\"><strong>🍲 守关人的烟火气</strong><br>四条包子铺的蒸汽裹着肉香<br>回记绿豆糕甜了整条古城街<br>最不能错过浑锅宴——<br>铜锅里沸腾着山海关的豪迈</p><blockquote style=\"text-align: start;\">\"一座关，半部明清史\"你准备好迎接沧海的回响了吗？</blockquote><p><img src=\"http://**********:9090/files/download/1752918481588-山海关2.jpg\" alt=\"\" data-href=\"\" style=\"width: 581.32px;height: 359.15px;\"/></p>', '2025-07-19 17:48:15', 3, 4, '通过');
INSERT INTO `article` VALUES (5, '🌄「黄山行云录」：在1600米云端，邂逅中国最写意的水墨山河', 'http://**********:9090/files/download/1753022163866-黄山01.jpg', '<p style=\"text-align: start;\"><strong>「登一次黄山，便懂了中国人骨子里的山水情结」第一重惊喜：泼墨云海</strong><br>清晨的始信峰畔<br>云浪吞没了万千奇松<br>忽而风来，群峰如岛浮出<br>——这是大自然最奢侈的“留白艺术”<br>（📌观云海最佳点：清凉台/狮子峰/排云亭）</p><p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1753022299630-山海关1.jpg\" alt=\"\" data-href=\"\" style=\"width: 507.32px;height: 380.49px;\"></p><p style=\"text-align: start;\">🗿 <strong>第二重震撼：石上生花</strong><br>“梦笔生花”的松尖挑着朝阳<br>“猴子观海”的剪影静默千年<br>每一块怪石都在讲述<br>《山海经》里跑出来的神话</p><p style=\"text-align: start;\">🌲 <strong>第三重风骨：悬崖松韵</strong><br>迎客松的枝桠伸展了800年<br>黑虎松的针叶扎进花岗岩<br>这些绝壁上的舞者<br>用根系写满生存的史诗</p><p style=\"text-align: start;\"><strong>🔖 行者备忘录</strong><br>▫️经典路线：云谷索道上→始信峰→北海→西海大峡谷（小火车上行）→光明顶日落<br>▫️隐秘玩法：丹霞峰看晚霞人少景绝，白云溪畔可寻黄山短尾猴<br>▫️必尝：山顶“黄山一绝”臭鳜鱼，鲜笋烧肉配毛峰茶</p><p style=\"text-align: start;\"><br></p><blockquote style=\"text-align: start;\">“五岳归来不看山，黄山归来不看岳”这趟云端之旅，你准备何时启程？</blockquote><p style=\"text-align: start;\">#安徽旅游天花板 #被国家地理推荐的一生必去 #松石云泉四绝</p>', '2025-07-20 22:38:25', 7, 5, '通过');
INSERT INTO `article` VALUES (6, '🤢「兴隆山生存指南」：在“美食荒漠”和“山路马拉松”中艰难求生的日子', 'http://**********:9090/files/download/1753092241467-兴隆山.jpg', '<p style=\"text-align: start;\"><strong>标题：</strong><br>🤢「兴隆山生存指南」：在“美食荒漠”和“山路马拉松”中艰难求生的日子</p><p style=\"text-align: start;\"><strong>正文：</strong></p><p style=\"text-align: start;\"><strong>「欢迎来到山东大学兴隆山校区——<br>一个让你减肥成功率和退学率同步飙升的地方」</strong></p><p style=\"text-align: start;\">🍽️ <strong>食堂篇：舌尖上的绝望</strong><br>• <strong>“猪食”传说</strong>：<br>号称“济南高校食堂倒数前三”的兴隆山食堂，<br>青菜能炒出洗洁精味，<br>肉菜里找肉堪比考古发掘。<br>（📌 推荐勇敢者挑战“兴隆山特色”——<br>永远黏糊糊的酱香饼和馊味随机出现的凉拌菜）</p><p style=\"text-align: start;\">• <strong>价格刺客</strong>：<br>10块钱的“精品套餐”里，<br>土豆丝占80%，<br>剩下20%是厨师的手抖艺术。</p><p style=\"text-align: start;\">🏃 <strong>交通篇：每日登山集训</strong><br>• 从宿舍到教学楼=半程马拉松，<br>坡度陡到让你怀疑在参加《极限挑战》。<br>（💡 温馨提示：早八课建议提前1小时出发）</p><p style=\"text-align: start;\">• 校车？不存在的。<br>“共享单车坟场”才是真实景观，<br>能找到一辆能骑的车算你中彩票。</p><p style=\"text-align: start;\">🛏️ <strong>生活篇：现代版“上山下乡”</strong><br>• 宿舍热水器时好时坏，<br>冬天洗澡靠勇气，夏天洗澡靠耐力。</p><p style=\"text-align: start;\">• 快递站远到像在另一个校区，<br>取个快递微信步数直接破万。</p><p style=\"text-align: start;\"><strong>🔖 生存技巧（血泪版）</strong><br>▫️ 囤泡面！囤泡面！囤泡面！<br>▫️ 加入外卖抢单群（虽然送餐小哥经常迷路）<br>▫️ 把爬山当健身（毕竟别无选择）</p><blockquote style=\"text-align: start;\">“在这里待四年，你能获得：坚强的胃、发达的腿肌、和对泡面的无限创意开发能力”</blockquote><p style=\"text-align: start;\">#高校生存实录 #饿梦回忆录 #读研不如出家</p>', '2025-07-21 18:05:47', 8, 5, '通过');

-- ----------------------------
-- Table structure for business
-- ----------------------------
DROP TABLE IF EXISTS `business`;
CREATE TABLE `business`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商家账号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '账号密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商家名称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色权限',
  `province_id` int NULL DEFAULT NULL COMMENT '省份',
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '地址',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评论',
  `price` double NULL DEFAULT NULL COMMENT '价格',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电子邮箱',
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '官网链接',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '宾馆图片',
  `leader` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '负责人',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  `front` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证正面',
  `back` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证反面',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '状态',
  `license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `score` double(10, 2) UNSIGNED NULL DEFAULT 0.00,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of business
-- ----------------------------
INSERT INTO `business` VALUES (3, 'admin1', '123456', '如家酒店', 'http://**********:9090/files/download/1751963096313-heidashuai1.jpg', 'BUSINESS', 1, '河北省衡水市桃城区榕花北大街398号', '如家酒店是中国知名的经济型连锁酒店品牌之一，成立于2002年，隶属于首旅如家酒店集团。 它以“宾至如家”的理念，致力于为每一位顾客提供温馨舒适的住宿体验。 无论是商务出差还是休闲旅游，如家酒店都能满足您的需求。 如家酒店的特点有哪些？ 🌟 如家酒店以其简洁明快的设计风格和干净整洁的房间环境著称。 客房内配备有基本的生活设施，包括舒适的床铺、洗浴用品和免费Wi-Fi。', 400, '***********', '<EMAIL>', 'https://bthhotels.com/', 'http://**********:9090/files/download/1751963019315-一舍山居.jpg', '张先生', '3333333334561', 'http://**********:9090/files/download/1751686865001-身份证正面.png', 'http://**********:9090/files/download/1751687303182-身份证正面.png', '通过', 'http://**********:9090/files/download/1751687266928-许可证.webp', 3.25);
INSERT INTO `business` VALUES (4, 'admin2', '123456', '锦江之星', 'http://**********:9090/files/download/1751690649651-微信图片_20250701105924_16.jpg', 'BUSINESS', 6, '北京市朝阳区延静西里', '酒店设施：提供24小时前台、免费Wi-Fi、早餐厅等基础服务，部分门店设有停车场、自助洗衣房，但通常不配备健身房等中高端酒店设施1。\n房间设施：房间面积一般在18～25平方米左右，布局紧凑合理，设施简单实用，包括床、椅子、桌子、电视、热水壶等，以保障基础睡眠质量为核心1。', 500, '***********', '<EMAIL>', NULL, 'http://**********:9090/files/download/1751690620396-天柱山徽舍小院民宿.jpg', '1111', '2222222', 'http://**********:9090/files/download/1751690635374-身份证正面.png', 'http://**********:9090/files/download/1751985113295-身份证反面.png', '通过', 'http://**********:9090/files/download/1751690626352-许可证.webp', 0.00);
INSERT INTO `business` VALUES (7, 'admin3', '123456', '王家宾馆', 'http://**********:9090/files/download/1751985317565-微信图片_20250707171715.jpg', 'BUSINESS', 7, '上海市徐汇区漕溪北路439号', '以上海命名的上海宾馆是经国务院批准，为接待外国旅游者、港澳同胞和华侨而兴建的四星级国际旅游宾馆。酒店地处市中心，交通便利，楼高91.5米，楼高30层。普标17.5平米，拥有双人房、单人房以及不同风格的套房共561间（套），房间色调高雅、安静舒适，是一座具有现代化设施集吃、住、行、玩、购一体的宾馆', 500, '***********', '<EMAIL>', 'https://cn.bing.com/', 'http://**********:9090/files/download/1751985049058-wws头像.png', '王小兵', '23456789', 'http://**********:9090/files/download/1751984916776-身份证正面.png', NULL, '通过', 'http://**********:9090/files/download/1751984912968-许可证.webp', 0.00);

-- ----------------------------
-- Table structure for business_orders
-- ----------------------------
DROP TABLE IF EXISTS `business_orders`;
CREATE TABLE `business_orders`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单编号',
  `user_id` int NULL DEFAULT NULL,
  `type_id` int NULL DEFAULT NULL,
  `business_id` int NULL DEFAULT NULL,
  `start` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '入住时间',
  `end` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '离开时间',
  `price` double NULL DEFAULT NULL,
  `pay_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `pay_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `score` double(10, 2) NULL DEFAULT 0.00,
  `comment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '评价',
  `comment_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of business_orders
-- ----------------------------
INSERT INTO `business_orders` VALUES (2, '20250729172742', 4, 7, 3, '2025-07-30', '2025-07-31', 400, NULL, '', '已取消', 0.00, NULL, '2025-07-31 11:21:33');
INSERT INTO `business_orders` VALUES (3, '20250730102300', 6, 6, 3, '2025-07-31', '2025-08-05', 3500, '0ff37c22-5a9e-4cac-bd60-4b6b21ea68f3', '2025-07-30 10:23:23', '已退房', 2.00, '较好', '2025-07-31 11:21:33');
INSERT INTO `business_orders` VALUES (5, '20250730221814', 4, 4, 3, '2025-08-01', '2025-08-05', 5192, 'bc5322aa-b6e3-490b-b46f-629665605994', '2025-07-30 22:18:43', '已退房', 0.00, NULL, '2025-07-31 11:21:33');
INSERT INTO `business_orders` VALUES (6, '20250731103440', 5, 6, 3, '2025-08-01', '2025-08-05', 2800, '4549add2-0f10-43d1-8ebc-edbe3c4ba87d', '2025-07-31 10:35:36', '已退房', 3.00, '这间房间较好，就是有点吵', '2025-07-31 11:21:33');
INSERT INTO `business_orders` VALUES (7, '20250731112004', 6, 7, 3, '2025-08-01', '2025-08-03', 800, '90c342e3-5b0e-4dd4-9309-62996ed3034f', '2025-07-31 11:20:17', '已退房', 4.00, '较好', '2025-07-31 11:21:33');
INSERT INTO `business_orders` VALUES (8, '20250731151513', 6, 7, 3, '2025-08-01', '2025-08-02', 400, '90b6bb1a-89bc-4afb-9607-c259cc6c9f2c', '2025-07-31 15:15:21', '已入住', 0.00, NULL, NULL);
INSERT INTO `business_orders` VALUES (9, '20250731170536', 4, 5, 4, '2025-08-01', '2025-08-04', 3900, '84ccf38f-cc96-4379-aa2e-6046e82aaadb', '2025-07-31 17:05:49', '待入住', 0.00, NULL, NULL);
INSERT INTO `business_orders` VALUES (10, '20250731212057', 4, 8, 7, '2025-08-01', '2025-08-03', 1100, NULL, NULL, '待支付', 0.00, NULL, NULL);

-- ----------------------------
-- Table structure for collect
-- ----------------------------
DROP TABLE IF EXISTS `collect`;
CREATE TABLE `collect`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type_id` int NULL DEFAULT NULL,
  `user_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of collect
-- ----------------------------
INSERT INTO `collect` VALUES (5, 6, 6);
INSERT INTO `collect` VALUES (7, 5, 4);
INSERT INTO `collect` VALUES (8, 4, 4);
INSERT INTO `collect` VALUES (9, 6, 5);
INSERT INTO `collect` VALUES (10, 6, 4);
INSERT INTO `collect` VALUES (11, 7, 6);

-- ----------------------------
-- Table structure for comment
-- ----------------------------
DROP TABLE IF EXISTS `comment`;
CREATE TABLE `comment`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NULL DEFAULT NULL,
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '内容',
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '时间',
  `article_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of comment
-- ----------------------------
INSERT INTO `comment` VALUES (3, 5, '这篇帖子我只能给82分，因为剩下的18分要以666的形式给', '2025-07-24 16:44:26', 1);
INSERT INTO `comment` VALUES (5, 4, '这个帖子写的很好，对我很有帮助', '2025-07-25 10:54:56', 3);
INSERT INTO `comment` VALUES (6, 4, '1111', '2025-07-25 12:15:21', 4);
INSERT INTO `comment` VALUES (7, 6, '好帖子', '2025-07-28 10:43:21', 6);
INSERT INTO `comment` VALUES (8, 6, '这个帖子太棒了，对我很有帮助', '2025-07-29 10:53:06', 1);
INSERT INTO `comment` VALUES (9, 6, '帖子很好', '2025-07-31 11:34:17', 3);

-- ----------------------------
-- Table structure for feedback
-- ----------------------------
DROP TABLE IF EXISTS `feedback`;
CREATE TABLE `feedback`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标题',
  `question` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '问题',
  `idea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '想法',
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `user_id` int NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '回复人',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '回复内容',
  `reply_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '回复状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of feedback
-- ----------------------------
INSERT INTO `feedback` VALUES (3, '无问题', '系统正常运行', NULL, '2025-07-10 17:29:36', 1, '111', '已收到', '2025-07-10 17:30:21', '已回复');
INSERT INTO `feedback` VALUES (6, '222', '333', '五想法', '2025-07-10 17:49:04', 4, '黑大帅', '好的', '2025-07-10 17:51:27', '已回复');
INSERT INTO `feedback` VALUES (7, '无问题', '111', '111', '2025-07-19 18:11:12', 4, 'Gypsophila。', '已收到，正在改正', '2025-07-19 18:11:43', '已回复');
INSERT INTO `feedback` VALUES (9, '加载慢', '系统正常运行，但加载较慢', '可不可以让系统图片加载更快一些', '2025-07-22 10:50:00', 6, NULL, NULL, NULL, '等待回复');

-- ----------------------------
-- Table structure for notice
-- ----------------------------
DROP TABLE IF EXISTS `notice`;
CREATE TABLE `notice`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公告标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '公告内容',
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of notice
-- ----------------------------
INSERT INTO `notice` VALUES (6, '商家信息开发', '对于商家信息的开发', '2025-07-04 18:33:35');
INSERT INTO `notice` VALUES (7, '反馈功能', '反馈功能可以正常运行', '2025-07-12 18:06:28');
INSERT INTO `notice` VALUES (8, '新功能', '系统公告功能开发完成', '2025-07-12 18:16:37');
INSERT INTO `notice` VALUES (9, '权限控制', '商家，用户，管理员三种角色权限控制', '2025-07-12 18:17:19');
INSERT INTO `notice` VALUES (10, '房间信息，景点信息，房间类型', '新增房间信息，景点信息，房间类型功能，初步完成旅游帖子页面。', '2025-07-21 20:58:19');

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '特产名称',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `province_id` int NULL DEFAULT NULL,
  `price` double NULL DEFAULT NULL COMMENT '价格',
  `num` int NULL DEFAULT 0,
  `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of product
-- ----------------------------
INSERT INTO `product` VALUES (1, '北京烤鸭', 'http://**********:9090/files/download/1753021236060-北京烤鸭.jpg', '北京烤鸭——中华美食瑰宝，以酥脆鸭皮与嫩滑肉质闻名于世。\n历史渊源：源于明朝宫廷御膳，600年传承，全聚德（1864年创立）将其发扬光大，成为北京标志性美食。\n独特工艺：\n✅ 果木挂炉（枣木/梨木）：赋予鸭肉独特果香\n✅ 皮肉分离：吹气、烫皮等18道工序，成就\"琥珀色脆皮\"\n✅ 片鸭艺术：师傅现场片出108片，薄如蝉翼\n经典吃法：\n🦆 荷叶饼卷鸭肉+甜面酱+葱丝+黄瓜条\n🍯 蘸白糖体验\"入口即化\"的酥皮\n文化地位：国宴首选，联合国非遗备选名录，被誉为\"世界级美味\"。\n\"不到长城非好汉，不吃烤鸭真遗憾！\"', 6, 100, 5, '只');
INSERT INTO `product` VALUES (3, '衡水湖烤鸭蛋', 'http://**********:9090/files/download/1753022046626-梨膏糖.png', '衡水湖烤鸭蛋——河北特色非遗美食，精选衡水湖散养麻鸭蛋，古法黄泥腌制45天，果木炭火慢烤至流油起沙，蛋白弹嫩如琥珀，蛋黄绵密咸香，富含卵磷脂与氨基酸，真空包装锁鲜，佐餐拌粥皆宜，是「可以吸着吃的烤鸭蛋」。#衡水特产 #非遗美食 #佐餐神器', 1, 10, 96, '个');
INSERT INTO `product` VALUES (4, '南翔小笼包', 'http://**********:9090/files/download/1753022570819-南翔小笼包.jpeg', '非遗美食，皮薄馅丰，蟹粉鲜肉爆汁，豫园老店必尝。', 7, 30, 6, '笼');

-- ----------------------------
-- Table structure for product_orders
-- ----------------------------
DROP TABLE IF EXISTS `product_orders`;
CREATE TABLE `product_orders`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键Id',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `user_id` int NULL DEFAULT NULL,
  `product_id` int NULL DEFAULT NULL,
  `num` int NULL DEFAULT NULL,
  `price` double NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `pay_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `pay_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of product_orders
-- ----------------------------
INSERT INTO `product_orders` VALUES (1, '20250722154554', 4, 1, 1, 100, '张先生', '***********', '山东大学兴隆山校区', '2025-07-22 15:45:55', 'c98c0cf3-ef01-4dfb-858b-86e67faeb1fb', '2025-07-22 18:48:01', '待发货');
INSERT INTO `product_orders` VALUES (2, '20250722155810', 4, 1, 2, 200, '张先生', '***********', '山东大学兴隆山校区', '2025-07-22 15:58:10', '08d8264f-d2c1-4cac-bf4b-40b4473c605d', '2025-07-22 18:44:28', '待发货');
INSERT INTO `product_orders` VALUES (3, '20250722185438', 4, 1, 1, 100, '张先生', '***********', '山东大学兴隆山校区', '2025-07-22 18:54:38', '6a7250a4-c1ea-4d4e-af43-01ba34990bc6', '2025-07-22 18:54:43', '待发货');
INSERT INTO `product_orders` VALUES (4, '20250722185519', 6, 4, 1, 30, '张先生', '***********', '山东大学兴隆山校区', '2025-07-22 18:55:19', '4c851292-6710-4615-8c77-27b43039e936', '2025-07-22 18:56:13', '待发货');
INSERT INTO `product_orders` VALUES (5, '20250722185747', 6, 4, 3, 90, '张先生', '***********', '山东大学兴隆山校区', '2025-07-22 18:57:47', '54272c45-bcb1-4d18-a76d-2a6c316414dc', '2025-07-22 18:58:28', '待发货');
INSERT INTO `product_orders` VALUES (6, '20250723101913', 6, 3, 1, 10, '张先生', '***********', '山东大学兴隆山校区', '2025-07-23 10:19:13', '19e2552c-c80d-4726-a661-b63d2aa47d3f', '2025-07-23 10:19:18', '待签收');
INSERT INTO `product_orders` VALUES (7, '20250723101946', 6, 3, 1, 10, '张先生', '***********', '山东大学兴隆山校区', '2025-07-23 10:19:46', '2f0f41aa-0dbe-43e3-a662-7c97a902bf3b', '2025-07-23 10:20:06', '已签收');
INSERT INTO `product_orders` VALUES (9, '20250723161001', 4, 3, 5, 50, '张三', '11111111', '山东大学软件园校区', '2025-07-23 16:10:01', NULL, NULL, '已取消');
INSERT INTO `product_orders` VALUES (12, '20250723163821', 4, 4, 1, 30, '李四', '19966668888', '山东省爱国区666号', '2025-07-23 16:38:21', NULL, NULL, '已取消');
INSERT INTO `product_orders` VALUES (13, '20250723164710', 6, 1, 2, 200, '刘六', '18899996666', '山东省高新区999号', '2025-07-23 16:47:10', '30c3211a-587e-468e-899c-bdc334c38754', '2025-07-23 16:47:47', '已签收');

-- ----------------------------
-- Table structure for province
-- ----------------------------
DROP TABLE IF EXISTS `province`;
CREATE TABLE `province`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of province
-- ----------------------------
INSERT INTO `province` VALUES (1, '河北');
INSERT INTO `province` VALUES (2, '山东');
INSERT INTO `province` VALUES (3, '河南');
INSERT INTO `province` VALUES (4, '山西');
INSERT INTO `province` VALUES (5, '天津');
INSERT INTO `province` VALUES (6, '北京');
INSERT INTO `province` VALUES (7, '上海');
INSERT INTO `province` VALUES (8, '浙江');

-- ----------------------------
-- Table structure for registration
-- ----------------------------
DROP TABLE IF EXISTS `registration`;
CREATE TABLE `registration`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `user_id` int NULL DEFAULT NULL,
  `business_id` int NULL DEFAULT NULL,
  `room_id` int NULL DEFAULT NULL,
  `in_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `out_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `type_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of registration
-- ----------------------------
INSERT INTO `registration` VALUES (2, '20250730102300', 6, 3, 11, '2025-08-01', '2025-07-31 11:00:17', 6);
INSERT INTO `registration` VALUES (3, '20250730110556', 5, 4, 12, '2025-08-01', '2025-07-30 22:01:42', 5);
INSERT INTO `registration` VALUES (4, '20250730221814', 4, 3, 3, '2025-08-01', '2025-07-31 11:20:53', 4);
INSERT INTO `registration` VALUES (5, '20250731103440', 5, 3, 9, '2025-08-02', '2025-07-31 10:57:49', 6);
INSERT INTO `registration` VALUES (6, '20250731112004', 6, 3, 5, '2025-08-01', '2025-07-31 11:20:51', 7);
INSERT INTO `registration` VALUES (7, '20250731151513', 6, 3, 5, '2025-08-01', NULL, 7);

-- ----------------------------
-- Table structure for room
-- ----------------------------
DROP TABLE IF EXISTS `room`;
CREATE TABLE `room`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type_id` int NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `business_id` int NULL DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of room
-- ----------------------------
INSERT INTO `room` VALUES (2, 4, 'WW-YX02-01', '2楼', 3, '正在维修');
INSERT INTO `room` VALUES (3, 4, 'WW-YX01-01', '1楼', 3, '空闲');
INSERT INTO `room` VALUES (4, 5, 'WW-YX02-01', '2楼', 4, '使用中');
INSERT INTO `room` VALUES (5, 7, 'SHW-03-01', '3楼', 3, '使用中');
INSERT INTO `room` VALUES (6, 4, 'WW-YX01-03', '3楼', 3, '空闲');
INSERT INTO `room` VALUES (7, 8, 'hhh-001-01', '1楼', 7, '空闲');
INSERT INTO `room` VALUES (8, 6, 'WW-YX02-02', '2楼', 3, '空闲');
INSERT INTO `room` VALUES (9, 6, 'WW-YX02-01', '2楼', 3, '空闲');
INSERT INTO `room` VALUES (10, 6, 'WW-YX01-01', '1楼', 3, '空闲');
INSERT INTO `room` VALUES (11, 6, 'WW-YX01-02', '1楼', 3, '空闲');
INSERT INTO `room` VALUES (12, 5, 'WW-YX01-02', '1楼', 4, '空闲');
INSERT INTO `room` VALUES (13, 7, 'SHW-01-02', '1楼', 3, '空闲');

-- ----------------------------
-- Table structure for scenery
-- ----------------------------
DROP TABLE IF EXISTS `scenery`;
CREATE TABLE `scenery`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '景点图片',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `province_id` int NULL DEFAULT NULL,
  `recommend` int NULL DEFAULT NULL,
  `views` int NULL DEFAULT 0 COMMENT '浏览量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of scenery
-- ----------------------------
INSERT INTO `scenery` VALUES (1, 'http://**********:9090/files/download/1752716746997-长城.jpg', '万里长城', '八达岭长城位于中国北京市延庆区，是明长城最具代表性的一段，也是世界上最著名的长城景点之一。', '<h2 style=\"text-align: center;\">北京-八达岭长城</h2><h3 style=\"text-align: start;\">历史背景</h3><p style=\"text-align: start; line-height: 2;\"><span style=\"font-size: 16px;\">八达岭长城的历史可以追溯到明朝（1368-1644年），为了防御北方游牧民族的侵袭，这段长城得到了加强和修缮。它是居庸关的重要前哨，古语云“居庸之险不在关而在八达岭”，凸显了其战略重要性。</span></p><p style=\"text-align: center; line-height: 2;\"><img src=\"http://43.142.9.148:9099/files/download/1723627162453-%E5%85%AB%E8%BE%BE%E5%B2%AD%E9%95%BF%E5%9F%8E03.png\" alt=\"\" data-href=\"\" style=\"width: 490.30px;height: 327.81px;\"></p><h3 style=\"text-align: start;\">地理位置与规模</h3><p style=\"text-align: start; line-height: 2;\"><span style=\"font-size: 16px;\">八达岭长城位于北京市延庆区军都山关沟古道北口，距离北京市中心天安门广场约70公里。这段长城全长近5千米，可供游览的城墙约3700多米，开放区域在不断延长，最近一次扩展是在2023年12月，增加了1245米，从南七楼延伸至南十六楼半。</span></p><p style=\"text-align: center; line-height: 2;\"><img src=\"http://43.142.9.148:9099/files/download/1723627184820-%E5%85%AB%E8%BE%BE%E5%B2%AD%E9%95%BF%E5%9F%8E05.png\" alt=\"\" data-href=\"\" style=\"width: 492.32px;height: 299.59px;\"></p><h3 style=\"text-align: start;\">景区概况</h3><ul><li style=\"text-align: start;\"><span style=\"font-size: 16px;\"><strong>主要景点</strong></span><span style=\"font-size: 16px;\">：八达岭长城景区以长城为主体，包括敌楼、烽火台、瞭望塔等历史遗迹，以及好汉坡、飞客的长城脚下等特色景观。</span></li><li style=\"text-align: start;\"><span style=\"font-size: 16px;\"><strong>配套设施</strong></span><span style=\"font-size: 16px;\">：景区内有八达岭饭店、中国长城博物馆、长城脚下的电影院、中华文化名人雕塑纪念园、詹天佑纪念馆、恐龙化石展览馆等。</span></li><li style=\"text-align: start;\"><span style=\"font-size: 16px;\"><strong>游览体验</strong></span><span style=\"font-size: 16px;\">：游客可以从关城出发，向南或北侧攀登长城，体验“不到长城非好汉”的豪情。此外，夜长城景观自2024年4月30日起开放，提供独特的夜间游览体验。</span></li></ul><p style=\"text-align: center;\"><img src=\"http://43.142.9.148:9099/files/download/1723627198122-%E5%85%AB%E8%BE%BE%E5%B2%AD%E9%95%BF%E5%9F%8E04.png\" alt=\"\" data-href=\"\" style=\"width: 694.32px;height: 424.54px;\"></p><h3 style=\"text-align: start;\">开放时间与票价</h3><ul><li style=\"text-align: start; line-height: 2;\"><span style=\"font-size: 16px;\"><strong>开放时间</strong></span><span style=\"font-size: 16px;\">：旺季（4月1日至10月31日）每日6:30至16:30开放，淡季（11月1日至次年3月31日）每日7:30至16:00开放。</span></li><li style=\"text-align: start; line-height: 2;\"><span style=\"font-size: 16px;\"><strong>门票价格</strong></span><span style=\"font-size: 16px;\">：旺季门票40元，淡季35元。60岁以上老年人、离休人员、军人、残疾人及1.2米以下儿童免门票。</span></li></ul><p style=\"text-align: center; line-height: 2;\"><img src=\"http://43.142.9.148:9099/files/download/1723627216710-%E5%85%AB%E8%BE%BE%E5%B2%AD%E9%95%BF%E5%9F%8E02.png\" alt=\"\" data-href=\"\" style=\"width: 572.32px;height: 355.40px;\"></p><h3 style=\"text-align: start;\">交通信息</h3><ul><li style=\"text-align: start; line-height: 2;\"><span style=\"font-size: 16px;\"><strong>公交</strong></span><span style=\"font-size: 16px;\">：德胜门乘坐919快车或919直达车，每5分钟一班。</span></li><li style=\"text-align: start; line-height: 2;\"><span style=\"font-size: 16px;\"><strong>专线车</strong></span><span style=\"font-size: 16px;\">：从天安门出发的专线车，A线160元含定陵和八达岭门票，C线100元含门票。</span></li></ul><h3 style=\"text-align: start;\">游览提示</h3><ul><li style=\"text-align: start;\"><span style=\"font-size: 16px;\">避开节假日高峰时段，尤其是上午10:00至11:00和下午14:00至15:00。</span></li><li style=\"text-align: start;\"><span style=\"font-size: 16px;\">长城部分地段坡度较陡，建议穿着防滑舒适的鞋子。</span></li></ul><p style=\"text-align: start; line-height: 1.5;\"><span style=\"font-size: 16px;\">八达岭长城不仅是中国古代防御体系的杰作，也是中外游客向往的旅游目的地，以其雄伟壮观的景象、深厚的历史文化底蕴而闻名于世。</span></p>', 6, 3, 33);
INSERT INTO `scenery` VALUES (2, 'http://**********:9090/files/download/1752717788120-衡水湖.jpg', '衡水湖', '衡水湖位于河北省衡水市，是华北平原上最大的淡水湖泊之一，被誉为“京津冀之肾”。作为国家级自然保护区和国家4A级旅游景区，衡水湖总面积约163平方公里，其中水域面积75平方公里，湿地生态系统保存完整，生物多样性极为丰富。这里是全球极危物种青头潜鸭的重要栖息地，每年吸引数以万计的候鸟在此停歇或越冬，是观鸟爱好者的理想胜地。', '<h3 style=\"text-align: start; line-height: 1.5;\"><strong>衡水湖国家级自然保护区</strong></h3><ul><li style=\"text-align: start;\">地位：华北平原唯一保持完整湿地生态系统的国家级自然保护区，被誉为\"京津冀最美湿地\"。</li><li style=\"text-align: start;\">特色：水域广阔：面积达75平方公里，是华北单体面积最大的淡水湖。生物多样性：栖息着310种鸟类（包括珍稀的青头潜鸭、白枕鹤），被誉为\"东亚蓝宝石\"。四季景观：春夏观荷苇荡漾，秋冬赏候鸟迁徙，冬季可体验冰上项目。文化结合：周边有冀州古城遗址、竹林寺等历史人文景点。</li><li style=\"text-align: start;\">活动推荐：乘船游湖、观鸟摄影、骑行环湖绿道。参观衡水湖湿地生态馆，了解生态系统。</li></ul><p style=\"text-align: start;\"><strong>其他备选景点</strong>：</p><ul><li style=\"text-align: start;\">衡水中学（教育旅游热点，需提前确认开放政策）。</li><li style=\"text-align: start;\">宝云塔（唐代古塔，衡水市区地标）。</li></ul><p style=\"text-align: start;\">若偏好自然风光，衡水湖是最具代表性的选择；若对人文感兴趣，可结合周边古迹规划行程。</p><p style=\"text-align: start;\"><br></p><p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1752717811896-衡水湖.jpg\" alt=\"\" data-href=\"\" style=\"width: 488.32px;height: 298.85px;\"></p>', 1, 4, 17);
INSERT INTO `scenery` VALUES (3, 'http://**********:9090/files/download/1752718023670-迪士尼.jpg', '上海迪士尼', '上海迪士尼乐园是中国大陆首座迪士尼主题乐园，拥有七大主题园区，包括全球独有的\"宝藏湾\"和\"创极速光轮\"等标志性项目。梦幻童话城堡、精彩花车巡游及夜间烟火秀带来沉浸式体验，适合家庭、情侣及迪士尼粉丝。融合经典IP与中国特色，是华东地区最受欢迎的主题乐园之一。', '<h3 style=\"text-align: start; line-height: 1.5;\"><strong>上海迪士尼乐园详细介绍</strong></h3><h4 style=\"text-align: start;\"><strong>1. 基本信息</strong></h4><ul><li style=\"text-align: start;\">位置：上海市浦东新区川沙新镇黄赵路310号</li><li style=\"text-align: start;\">开放时间：2016年6月16日（中国大陆首座迪士尼乐园）</li><li style=\"text-align: start;\">面积：约3.9平方公里（全球第二大迪士尼乐园）</li><li style=\"text-align: start;\">主题园区：7大主题区 + 迪士尼小镇（免费开放）</li><li style=\"text-align: start;\">官方票价：常规日（成人）¥475起，高峰日¥659起，特别高峰日¥799起儿童、老年人、残障人士有优惠</li></ul><h4 style=\"text-align: start;\"><strong>2. 七大主题园区及主要游乐项目</strong></h4><h5 style=\"text-align: start;\"><strong>（1）米奇大街（Mickey Avenue）</strong></h5><ul><li style=\"text-align: start;\">特色：乐园主入口，仿造1920年代美国小镇风格，适合拍照、购物。</li><li style=\"text-align: start;\">亮点：米奇童话专列（花车巡游）：迪士尼经典角色巡游表演。甜心糖果屋：购买迪士尼主题甜品和纪念品。</li></ul><h5 style=\"text-align: start;\"><strong>（2）奇想花园（Gardens of Imagination）</strong></h5><ul><li style=\"text-align: start;\">特色：适合家庭游客，充满童话氛围。</li><li style=\"text-align: start;\">亮点项目：幻想曲旋转木马：迪士尼经典IP主题旋转木马。小飞象：适合低龄儿童的飞行体验。米奇俱乐部：与米奇合影互动。</li></ul><h5 style=\"text-align: start;\"><strong>（3）探险岛（Adventure Isle）</strong></h5><ul><li style=\"text-align: start;\">特色：以《疯狂动物城》《丛林奇谈》为灵感，充满原始丛林探险感。</li><li style=\"text-align: start;\">亮点项目：翱翔·飞越地平线（Soaring Over the Horizon）：5D球幕飞行体验，全球迪士尼热门项目。雷鸣山漂流：激流勇进式漂流，需备雨衣。古迹探索营：绳索挑战，适合喜欢冒险的游客。</li></ul><h5 style=\"text-align: start;\"><strong>（4）宝藏湾（Treasure Cove）</strong></h5><ul><li style=\"text-align: start;\">全球唯一加勒比海盗主题园区</li><li style=\"text-align: start;\">亮点项目：加勒比海盗——沉落宝藏之战：沉浸式乘船体验，特效震撼。探险家独木舟：手动划船游览海盗湾。风暴来临：杰克船长之惊天特技大冒险（舞台表演）。</li></ul><h5 style=\"text-align: start;\"><strong>（5）明日世界（Tomorrowland）</strong></h5><ul><li style=\"text-align: start;\">特色：科幻未来主题，科技感十足。</li><li style=\"text-align: start;\">亮点项目：创极速光轮（TRON Lightcycle Power Run）：全球迪士尼最刺激的过山车之一，摩托式乘坐体验。巴斯光年星际营救：互动射击游戏，适合亲子。喷气背包飞行器：高空旋转飞行体验。</li></ul><h5 style=\"text-align: start;\"><strong>（6）梦幻世界（Fantasyland）</strong></h5><ul><li style=\"text-align: start;\">特色：童话风格，适合儿童和少女心游客。</li><li style=\"text-align: start;\">亮点项目：七个小矮人矿山车：家庭版过山车，刺激但不过分。小熊维尼历险记：沉浸式童话故事体验。奇幻童话城堡（Enchanted Storybook Castle）：全球最高、最大的迪士尼城堡，可登顶俯瞰乐园。</li></ul><h5 style=\"text-align: start;\"><strong>（7）迪士尼·皮克斯玩具总动员（Toy Story Land）</strong></h5><ul><li style=\"text-align: start;\">特色：以《玩具总动员》为主题，色彩缤纷。</li><li style=\"text-align: start;\">亮点项目：抱抱龙冲天赛车：U型轨道弹射式过山车，刺激度较高。弹簧狗团团转：适合儿童的旋转游乐设施。胡迪牛仔嘉年华：欢乐的马车旋转项目。</li></ul><h4 style=\"text-align: start;\"><strong>3. 娱乐表演 &amp; 夜间秀</strong></h4><ul><li style=\"text-align: start;\">米奇童话专列（日间巡游）：迪士尼经典角色花车巡游</li></ul>', 7, 5, 26);
INSERT INTO `scenery` VALUES (4, 'http://**********:9090/files/download/1752718472849-西湖.jpg', '西湖', '特色：中国首个免费开放的5A级景区，湖光山色与人文古迹交融。\n\n亮点：断桥残雪、雷峰夕照、三潭印月，四季皆美。', '<p style=\"text-align: start;\"><strong>西湖</strong>位于浙江省杭州市中心，是中国首个免费开放的<strong>国家5A级旅游景区</strong>，也是<strong>世界文化遗产</strong>（2011年列入）。西湖以秀丽的湖光山色和深厚的文化底蕴闻名，自古被誉为\"人间天堂\"。</p><p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1752718580713-西湖.jpg\" alt=\"\" data-href=\"\" style=\"width: 606.32px;height: 402.45px;\"></p><p style=\"text-align: start;\"><strong>核心景观</strong>包括\"西湖十景\"：</p><ul><li style=\"text-align: start;\">苏堤春晓（春日杨柳依依）</li><li style=\"text-align: start;\">断桥残雪（《白蛇传》爱情传说地）</li><li style=\"text-align: start;\">雷峰夕照（晚霞映塔，含雷峰塔遗址）</li><li style=\"text-align: start;\">三潭印月（湖中石塔，1元人民币图案）</li><li style=\"text-align: start;\">平湖秋月（中秋赏月胜地）等。</li></ul><p style=\"text-align: start;\"><strong>特色体验</strong>：</p><ul><li style=\"text-align: start;\">环湖骑行（约15公里，免费）</li><li style=\"text-align: start;\">乘摇橹船游湖（推荐茅家埠路线）</li><li style=\"text-align: start;\">参观浙江省博物馆（孤山馆区藏有《富春山居图》剩山图）</li></ul><p style=\"text-align: start;\"><strong>文化底蕴</strong>：白居易、苏轼等文人留下大量诗篇，周边有<strong>岳王庙</strong>、<strong>灵隐寺</strong>等历史遗迹。四季皆宜游览，春季桃柳相映，夏季荷花满塘，秋季桂花飘香，冬季雪湖静谧。</p><p style=\"text-align: start;\"><strong>交通</strong>：地铁1号线至龙翔桥站，步行5分钟即达湖滨景区。</p><p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1752718571524-西湖1.jpg\" alt=\"\" data-href=\"\" style=\"width: 555.32px;height: 416.68px;\"></p>', 8, 5, 10);
INSERT INTO `scenery` VALUES (5, 'http://**********:9090/files/download/1752718767615-泰山.jpg', '泰山', '泰山位于山东省泰安市，素有“五岳之首”“天下第一山”之称，是中国首个世界文化与自然双重遗产（1987年列入）。主峰玉皇顶海拔1545米，以雄伟壮丽、文化底蕴深厚闻名，自古被视为帝王封禅、文人朝圣的圣地。', '<p style=\"text-align: start;\">泰山位于山东省泰安市，素有“五岳之首”“天下第一山”之称，是中国首个<strong>世界文化与自然双重遗产</strong>（1987年列入）。主峰玉皇顶海拔1545米，以雄伟壮丽、文化底蕴深厚闻名，自古被视为帝王封禅、文人朝圣的圣地。</p><p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1752718740890-泰山.jpg\" alt=\"\" data-href=\"\" style=\"width: 519.30px;height: 346.21px;\"></p><p style=\"text-align: start;\"><strong>核心景观</strong>：</p><ul><li style=\"text-align: start;\">十八盘：最险峻登山道，1600余级台阶直通南天门；</li><li style=\"text-align: start;\">岱庙：历代帝王祭祀泰山神的起点，现存宋天贶殿等古建；</li><li style=\"text-align: start;\">日观峰：观日出云海的绝佳位置；</li><li style=\"text-align: start;\">摩崖石刻：涵盖秦至清2000余处题刻，如唐玄宗《纪泰山铭》。</li></ul><p style=\"text-align: start;\"><strong>文化地位</strong>：<br>孔子“登泰山而小天下”，杜甫“会当凌绝顶”等名句均源于此，儒释道文化在此交融。</p><p style=\"text-align: start;\"><strong>游览建议</strong>：</p><ul><li style=\"text-align: start;\">徒步登山约4-6小时（红门路线经典），或乘缆车至中天门；</li><li style=\"text-align: start;\">四季皆宜，春秋最佳，日出、云海为奇观。</li></ul><p style=\"text-align: start;\"><strong>交通</strong>：泰安站（高铁）乘公交/出租车约30分钟抵达天外村或红门入口。</p><p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1752718747964-泰山1.jpg\" alt=\"\" data-href=\"\" style=\"width: 472.32px;height: 291.05px;\"></p>', 2, 3, 6);
INSERT INTO `scenery` VALUES (6, 'http://**********:9090/files/download/1752718889880-圆明园03.png', '上海外滩', '外滩（The Bund）位于上海市黄浦区，是上海最具标志性的景点之一，被誉为“万国建筑博览群”。这里汇聚了百年历史与现代繁华，是感受上海中西文化交融的绝佳地点。', '<p style=\"text-align: start;\"><strong>外滩</strong>（The Bund）位于上海市黄浦区，是上海最具标志性的景点之一，被誉为“万国建筑博览群”。这里汇聚了百年历史与现代繁华，是感受上海中西文化交融的绝佳地点。</p><h4 style=\"text-align: start;\"><strong>特色亮点</strong></h4><ul><li style=\"text-align: start;\">历史建筑群：沿江矗立着52栋风格各异的近代欧式建筑，如哥特式的和平饭店、巴洛克风格的海关大楼等，夜晚灯光璀璨，宛如“东方华尔街”。</li><li style=\"text-align: start;\">黄浦江风光：隔江对望陆家嘴金融区，东方明珠、上海中心大厦等摩天大楼与老建筑形成鲜明对比。</li><li style=\"text-align: start;\">外滩观景平台：最佳拍摄点，可欣赏浦江两岸日景与夜景，夜晚灯光秀不容错过。</li><li style=\"text-align: start;\">外白渡桥：百年钢桥，曾是老上海地标，影视剧热门取景地。</li></ul><h4 style=\"text-align: start;\"><strong>游览建议</strong></h4><ul><li style=\"text-align: start;\">最佳时间：傍晚至夜间（灯光19:00-23:00开启），可乘黄浦江游船全方位观景。</li><li style=\"text-align: start;\">周边联动：步行可达南京路步行街、豫园等景点。</li></ul><p style=\"text-align: start;\"><strong>交通</strong>：地铁2/10号线南京东路站，步行约10分钟。免费开放，全天可游。</p>', 7, 1, 7);
INSERT INTO `scenery` VALUES (7, 'http://**********:9090/files/download/1752819021803-山海关1.jpg', '山海关', '山海关位于河北省秦皇岛市，是明长城东端起点，素有\"天下第一关\"之称。这座始建于1381年的军事要塞，北倚燕山，南临渤海，因扼守华北与东北咽喉要道而闻名于世。关城高14米，气势恢宏，城门上\"天下第一关\"巨匾尤为醒目。景区内老龙头长城延伸入海，形成\"长城连海水连天\"的奇观，澄海楼、海神庙等古迹点缀其间。作为1644年吴三桂引清军入关的历史事件发生地，山海关见证了明清更替的关键时刻。如今，这座保存完好的军事要塞与周边角山长城、孟姜女庙等景点共同构成了一幅壮丽的历史画卷，是感受长城文化与明清历史的重要窗口。', '<h3 style=\"line-height: 1.5;\"><strong>山海关简介</strong></h3><p><strong>山海关</strong>位于河北省秦皇岛市，是明长城东端的重要关隘，素有“天下第一关”之称。始建于明洪武十四年（1381年），由大将徐达主持修建，因其北倚燕山、南临渤海，故得名“山海关”。这里是历史上连接华北与东北的咽喉要道，军事地位极其重要。</p><h4><strong>核心特色</strong></h4><ol><li>天下第一关城楼关城高14米，气势恢宏，城门上悬挂明代书法家萧显所题“天下第一关”巨匾，为山海关标志性景观。</li><li>长城文化体验老龙头：长城唯一一段入海处，宛如巨龙探海，可参观澄海楼、海神庙等遗迹。角山长城：山海关北部险峻段落，登高可俯瞰关城全景。</li><li>历史军事遗迹兵部分司署：明代军事管理机构旧址，展示古代边防制度。古城风貌：城内保留明清街巷格局，钟鼓楼、四合院等建筑古韵犹存。</li></ol><p><img src=\"http://**********:9090/files/download/1752818968903-山海关1.jpg\" alt=\"\" data-href=\"\" style=\"width: 494.32px;height: 370.74px;\"/></p><h4><strong>文化背景</strong></h4><ul><li>1644年，吴三桂在此引清兵入关，成为改变中国历史的关键节点，“冲冠一怒为红颜”的典故即源于此。</li></ul><p><img src=\"http://**********:9090/files/download/1752819007719-山海关3.jpg\" alt=\"\" data-href=\"\" style=\"width: 538.30px;height: 359.04px;\"/></p><h4><strong>游览贴士</strong></h4><ul><li>联票推荐：天下第一关+老龙头+孟姜女庙（约120元）。</li><li>最佳季节：5-10月，夏季可到老龙头海滨游玩。</li><li>交通：秦皇岛市区乘25路公交直达，或从北戴河站打车约30分钟。</li></ul><p>山海关集长城雄浑、海滨秀美与历史厚重于一体，是研究明清军事史和长城文化的必访之地。</p>', 1, 2, 5);
INSERT INTO `scenery` VALUES (8, 'http://**********:9090/files/download/1753092038750-兴隆山.jpg', '山东大学兴隆山校区', '山东大学兴隆山校区位于济南市市中区，是该校重点建设的现代化校区之一，以工科为特色，环境优美，设施先进。校区占地约1000亩，建筑融合现代风格与山水意境，毗邻兴隆山风景区，四季绿荫环绕，氛围宁静。', '<p style=\"text-align: start;\"><img src=\"http://**********:9090/files/download/1753092206600-兴隆山.jpg\" alt=\"\" data-href=\"\" style=\"width: 597.32px;height: 349.43px;\"></p><h4 style=\"text-align: start;\"><strong>1. 校区概况</strong></h4><p style=\"text-align: start;\">山东大学兴隆山校区位于<strong>济南市市中区二环南路12550号</strong>，是山东大学“一校三地”（济南、青岛、威海）办学格局中济南核心校区的重要组成部分。校区占地约<strong>1000亩</strong>，以工科为主，兼具科研与教学功能，环境优美，被誉为“<strong>山水校园</strong>”。</p><h4 style=\"text-align: start;\"><strong>2. 学院与学科特色</strong></h4><ul><li style=\"text-align: start;\">主要学院：机械工程学院控制科学与工程学院能源与动力工程学院材料科学与工程学院齐鲁交通学院（部分专业）</li><li style=\"text-align: start;\">学科优势：聚焦高端装备制造、智能控制、新能源等国家战略需求领域，拥有多个国家级重点实验室和省部级科研平台。</li></ul><h4 style=\"text-align: start;\"><strong>3. 校园设施</strong></h4><ul><li style=\"text-align: start;\">图书馆：现代化藏书空间，配备智能检索系统，馆藏以工科文献为主，设24小时自习区。</li><li style=\"text-align: start;\">工程实训中心：国家级实验教学示范中心，涵盖机器人、智能制造等实践教学模块。</li><li style=\"text-align: start;\">体育场馆：标准田径场、室内篮球馆、游泳馆及攀岩墙等设施。</li><li style=\"text-align: start;\">生活配套：学生食堂、商业街、快递服务中心、校医院等一应俱全。</li></ul><h4 style=\"text-align: start;\"><strong>4. 校园环境</strong></h4><ul><li style=\"text-align: start;\">自然景观：背靠兴隆山，校内保留原始山体绿地，步道蜿蜒，四季景色各异，尤以秋日红叶著称。</li><li style=\"text-align: start;\">建筑风格：融合现代设计与生态理念，教学楼与实验楼以灰白色调为主，与山势相映成趣。</li></ul><h4 style=\"text-align: start;\"><strong>5. 交通与周边</strong></h4><ul><li style=\"text-align: start;\">校内交通：电动摆渡车覆盖主要教学区与生活区。</li><li style=\"text-align: start;\">校外出行：公交：K52路直达市中心（泉城广场约40分钟），另有K94、B164路等。地铁：未来规划地铁环线（建设中）。</li><li style=\"text-align: start;\">周边地标：毗邻山东大学国家大学科技园，距济南南绕城高速入口仅5分钟车程。</li></ul><h4 style=\"text-align: start;\"><strong>6. 文化与学生活动</strong></h4><ul><li style=\"text-align: start;\">品牌活动：“兴隆山论坛”（学术讲座）工程创新竞赛（如智能车大赛）山地徒步节（秋季登山活动）</li><li style=\"text-align: start;\">学生社团：机器人协会、航模队等工科社团活跃。</li></ul><p style=\"text-align: start;\"><strong>#治学修身好去处 #工科生的山水学堂</strong><br>（配图建议：图书馆夜景/实验室操作场景/秋日校园全景）</p><hr/><p><br></p><p style=\"text-align: start;\"><strong>📍 地址</strong>：山东省济南市市中区二环南路12550号<br><strong>🚌 公交导航</strong>：搜索“山东大学兴隆山校区南门”</p>', 2, 3, 3);

-- ----------------------------
-- Table structure for type
-- ----------------------------
DROP TABLE IF EXISTS `type`;
CREATE TABLE `type`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `price` double NULL DEFAULT NULL,
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面图片',
  `content` longblob NULL COMMENT '详细介绍',
  `img1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `img2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `img3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `img4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `business_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of type
-- ----------------------------
INSERT INTO `type` VALUES (4, '大床房', '很好', 1298, 'http://**********:9090/files/download/1753435288967-印巷小院01.jpg', 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http://**********:9090/files/download/1752574260159-上海外滩江景Loft民宿_标准间03.jpg', 'http://**********:9090/files/download/1752574262992-上海外滩江景Loft民宿_标准间02.jpg', NULL, NULL, 3);
INSERT INTO `type` VALUES (5, '林西小院', '林西小院采用了现代与传统相结合的设计风格，既保留了当地村落的原始风貌，又融入了现代舒适的生活元素。小院内部装饰简约而温馨，使用大量自然材料如木材和石材，营造出亲近自然的居住体验。私人庭院内种有各种花草树木，提供了一个私密的休闲空间。勿忘山·归谷民宿周边环境幽静，空气清新，适合进行徒步、骑行等多种户外活动。民宿还提供了一些文化体验项目，如手工制作、农耕体验等，让客人能够更深入地了解当地文化。', 1300, 'http://**********:9090/files/download/1752636006450-天柱山徽舍小院民宿.jpg', 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http://**********:9090/files/download/1752636009732-陌上天鹅堡民宿.jpg', 'http://**********:9090/files/download/1752636013017-九华山云栖澜山民宿.jpg', NULL, NULL, 4);
INSERT INTO `type` VALUES (6, '双人间', '一舍山居位于北京市延庆区千家店镇某村，紧邻美丽的山水之间，距离北京市中心约90公里，是一个远离尘嚣、充满自然之美的隐秘之所。这里四季分明，春天花开满园，夏日绿意盎然，秋风送爽，冬雪皑皑，是逃离都市喧嚣的理想之地。', 700, 'http://**********:9090/files/download/1752658222766-天柱山徽舍小院民宿.jpg', 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http://**********:9090/files/download/1753692172268-听风小院02.jpg', 'http://**********:9090/files/download/1753692175111-听风小院03.jpg', NULL, NULL, 3);
INSERT INTO `type` VALUES (7, '标准间', '上海外滩江景Loft民宿位于上海市黄浦区，紧邻外滩，距离著名的外滩观景平台仅几步之遥。从房间内可以一览无遗地欣赏到黄浦江的壮丽景色和对岸陆家嘴的摩天大楼群。步行即可到达南京路步行街、豫园等著名景点，交通极为便利。', 400, 'http://**********:9090/files/download/1753268376480-上海外滩江景Loft民宿_标准间01.jpg', 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http://**********:9090/files/download/1753268381604-上海外滩江景Loft民宿_标准间03.jpg', 'http://**********:9090/files/download/1753268384047-上海外滩江景Loft民宿_标准间06.jpg', NULL, NULL, 3);
INSERT INTO `type` VALUES (8, '双人间', '空间宽敞，适合旅游居住', 550, 'http://**********:9090/files/download/1753435744808-一舍山居_双人间01.jpg', 0x3C703EE7A9BAE997B4E5AEBDE6959EEFBC8CE98082E59088E69785E6B8B8E5B185E4BD8F3C2F703E, 'http://**********:9090/files/download/1753435754184-一舍山居_双人间03.jpg', 'http://**********:9090/files/download/1753435751748-一舍山居_双人间02.jpg', NULL, NULL, 7);

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '账号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '姓名',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色权限',
  `account` double(10, 2) NULL DEFAULT 0.00 COMMENT '余额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'liu2', '123456', '张三', 'http://**********:9090/files/download/*************-黑大帅.jpg', '389401', '<EMAIL>', 'USER', 0.00);
INSERT INTO `user` VALUES (4, 'liu', '123456', '我非生来伶仃', 'http://**********:9090/files/download/*************-微信图片_20250707171715.jpg', '***********', '<EMAIL>', 'USER', 4709.00);
INSERT INTO `user` VALUES (5, 'liu3', '1234567', 'lcy', 'http://**********:9090/files/download/*************-头像1.jpg', '*********', '<EMAIL>', 'USER', 750.00);
INSERT INTO `user` VALUES (6, 'liu1', '123456', '春日部驱魔师', 'http://**********:9090/files/download/*************-20200407080547_gqcpq.png', '78967', '12345678', 'USER', 360.00);

SET FOREIGN_KEY_CHECKS = 1;
