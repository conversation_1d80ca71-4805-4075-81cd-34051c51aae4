<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.RegistrationMapper">

    <select id="selectAll" resultType="com.example.entity.Registration">
        select registration.*,user.name as userName,type.name as typeName,business.name as businessName,room.name as roomName from `registration`
        left join user on registration.user_id =user.id
        left join type on registration.type_id = type.id
        left join business on registration.business_id = business.id
        left join room on registration.room_id = room.id
        <where>
            <if test="orderNo != null"> and order_no = #{orderNo}</if>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="businessId != null"> and registration.business_id = #{businessId}</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `registration`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Registration" useGeneratedKeys="true">
        insert into `registration`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="typeId != null">type_id,</if>
            <if test="businessId != null">business_id,</if>
            <if test="roomId != null">room_id,</if>
            <if test="inTime != null">in_time,</if>
            <if test="outTime != null">out_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="roomId != null">#{roomId},</if>
            <if test="inTime != null">#{inTime},</if>
            <if test="outTime != null">#{outTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Registration">
        update `registration`
        <set>
            <if test="outTime != null">
                out_time = #{outTime},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>