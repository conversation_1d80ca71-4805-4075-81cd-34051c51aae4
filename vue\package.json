{"name": "vue", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.6.8", "echarts": "^5.5.1", "element-plus": "^2.7.2", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "sass": "^1.79.5", "unplugin-auto-import": "^0.17.5", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.8"}}