<template>
  <div style="width: 50%" class="card">
    <div style="color: red;padding: 10px">认证信息修改，账号将进入审核状态，直至管理员审核完成</div>
    <el-form ref="user" :model="data.user" label-width="90px" style="padding: 20px">
      <el-form-item prop="img" label="宾馆头像">
        <el-upload
            :action="baseUrl + '/files/upload'"
            :on-success="handleImgUpload"
            :show-file-list="false"
            class="avatar-uploader"
        >
          <img v-if="data.user.img" :src="data.user.img" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item prop="name" label="宾馆名称">
        <el-input  v-model="data.user.name" placeholder="请输入宾馆名称"></el-input>
      </el-form-item>
      <el-form-item prop="provinceId" label="所属省份">
        <el-select v-model="data.user.provinceId" placeholder="请选择省份" style="width: 100%">
          <el-option
              v-for="item in data.provinceData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="content" label="描述">
        <el-input  type="textarea" :rows="6" v-model="data.user.content" placeholder="请输入宾馆描述"></el-input>
      </el-form-item>
      <el-form-item prop="address" label="地址">
        <el-input  type="textarea" :rows="4" v-model="data.user.address" placeholder="请输入宾馆地址"></el-input>
      </el-form-item>
      <el-form-item prop="price" label="价格">
        <el-input-number v-model="data.user.price" :min="100" />
      </el-form-item>
      <el-form-item prop="link" label="链接">
        <el-input  v-model="data.user.link" placeholder="请输入官网链接"></el-input>
      </el-form-item>
      <el-form-item prop="license" label="营业执照">
        <el-upload
            :action="baseUrl + '/files/upload'"
            :on-success="handleLicenseUpload"
            :show-file-list="false"
            class="avatar-uploader"
        >
          <img v-if="data.user.license" :src="data.user.license" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item prop="leader" label="负责人姓名">
        <el-input   v-model="data.user.leader" placeholder="请输入负责人姓名"></el-input>
      </el-form-item>
      <el-form-item prop="code" label="身份证号">
        <el-input   v-model="data.user.code" placeholder="请输入负责人身份证号"></el-input>
      </el-form-item>
      <el-form-item prop="front" label="身份证正面">
        <el-upload
            :action="baseUrl + '/files/upload'"
            :on-success="handleFrontUpload"
            :show-file-list="false"
            class="avatar-uploader"
        >
          <img v-if="data.user.front" :src="data.user.front" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item prop="name" label="宾馆名称">
        <el-tag v-if="data.user.status ==='待审核'" type="warning">{{data.user.status}}</el-tag>
        <el-tag v-if="data.user.status ==='通过'" type="success">{{data.user.status}}</el-tag>
        <el-tag v-if="data.user.status ==='拒绝'" type="danger">{{data.user.status}}</el-tag>
      </el-form-item>
      <div style="text-align: center">
        <el-button type="primary" @click="update">提交修改申请</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { reactive } from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";

const baseUrl = import.meta.env.VITE_BASE_URL

const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
  provinceData:[]
})

const handleImgUpload = (res) => {
  data.user.img = res.data
}
const handleLicenseUpload = (res) =>{
  data.user.license = res.data
}
const handleFrontUpload = (res) =>{
  data.user.front = res.data
}
const loadProvince = () =>{
  request.get('province/selectAll').then(res => {
    if(res.code === '200'){
      data.provinceData = res.data
    } else{
      ElMessage.error(res.msg)
    }
  })
}
loadProvince()
const emit = defineEmits(['updateUser'])
const update = () => {
  if (data.user.role === 'BUSINESS') {
    data.user.status = '待审核'
    request.put('/business/update', data.user).then(res => {
      if (res.code === '200') {
        ElMessage.success('提交成功，等待管理员审核')
        localStorage.setItem('xm-user', JSON.stringify(data.user))
        emit('updateUser')
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}
</script>

<style>
.avatar-uploader {
  height: 120px;
}
.avatar-uploader .avatar {
  width: 120px;
  height: 120px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
}
</style>