package com.example.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.example.common.enums.RoleEnum;
import com.example.entity.*;
import com.example.exception.CustomException;
import com.example.mapper.*;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 宾馆订单业务层方法
 */
@Service
public class BusinessOrdersService {

    @Resource
    private BusinessOrdersMapper businessOrdersMapper;
    @Resource
    private TypeMapper typeMapper;

    @Resource
    private UserMapper userMapper;
    @Resource
    private BusinessMapper businessMapper;

    //下单
    public void add(BusinessOrders businessOrders) {
        //离开时间与入住时间
        DateTime startTime = DateUtil.parse(businessOrders.getStart());
        DateTime endTime = DateUtil.parse(businessOrders.getEnd());
        if(startTime.isAfterOrEquals(endTime)){
            throw new CustomException("500","离开时间必须在入住时间之后");
        }
        Account currentUser = TokenUtils.getCurrentUser();
        businessOrders.setUserId(currentUser.getId());
        businessOrders.setOrderNo(DateUtil.format(new Date(),"yyyyMMddHHmmss"));
        businessOrders.setStatus("待支付");
        Type type = typeMapper.selectById(businessOrders.getTypeId());
        if(ObjectUtil.isEmpty(type)){
            throw new CustomException("500","该房型已不存在");
        }
        long days =DateUtil.between(startTime,endTime, DateUnit.DAY);
        businessOrders.setPrice(days * type.getPrice());

        businessOrdersMapper.insert(businessOrders);
    }

    public void updateById(BusinessOrders businessOrders) {
        businessOrdersMapper.updateById(businessOrders);
    }



    public void deleteById(Integer id) {
        businessOrdersMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            businessOrdersMapper.deleteById(id);
        }
    }

    public BusinessOrders selectById(Integer id) {
        return businessOrdersMapper.selectById(id);
    }

    public List<BusinessOrders> selectAll(BusinessOrders businessOrders) {
        return businessOrdersMapper.selectAll(businessOrders);
    }

    public PageInfo<BusinessOrders> selectPage(BusinessOrders businessOrders, Integer pageNum, Integer pageSize) {
        Account currentUser = TokenUtils.getCurrentUser();
        if (RoleEnum.USER.name().equals(currentUser.getRole())) {
            businessOrders.setUserId(currentUser.getId());
        }
        if (RoleEnum.BUSINESS.name().equals(currentUser.getRole())) {
            businessOrders.setBusinessId(currentUser.getId());
        }
        PageHelper.startPage(pageNum, pageSize);
        List<BusinessOrders> list = businessOrdersMapper.selectAll(businessOrders);
        return PageInfo.of(list);
    }

    public void cancel(BusinessOrders businessOrders) {
       //取消订单，把订单状态进行更新
        businessOrders.setStatus("已取消");
        businessOrdersMapper.updateById(businessOrders);
    }

    public void pay(BusinessOrders businessOrders) {
        Integer id = businessOrders.getUserId();
        User user = userMapper.selectById(id);
        if(user.getAccount() < businessOrders.getPrice()){
            throw new CustomException("500","账户余额不足，请先前往充值");
        }
        user.setAccount(user.getAccount()- businessOrders.getPrice());
        userMapper.updateById(user);
        businessOrders.setPayTime(DateUtil.now());
        businessOrders.setPayNo(UUID.fastUUID().toString());
        businessOrders.setStatus("待入住");
        businessOrdersMapper.updateById(businessOrders);
    }

    public void comment(BusinessOrders businessOrders) {
        businessOrders.setCommentTime(DateUtil.now());
        businessOrdersMapper.updateById(businessOrders);
        //计算民宿总体评分
        Business business = businessMapper.selectById(businessOrders.getBusinessId());
        if(ObjectUtil.isNotEmpty(business)){
           Double score1=business.getScore();
           Double score2 = businessOrders.getScore();
           if(score1 == 0){
               business.setScore(score2);
           }else{
               business.setScore((score1+score2)/2);
           }
           businessMapper.updateById(business);
        }
    }
}


