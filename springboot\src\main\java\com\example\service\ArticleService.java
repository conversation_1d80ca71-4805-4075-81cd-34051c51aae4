package com.example.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HtmlUtil;
import com.example.common.enums.RoleEnum;
import com.example.entity.Account;
import com.example.entity.Article;
import com.example.entity.Comment;
import com.example.entity.User;
import com.example.mapper.ArticleMapper;
import com.example.mapper.CommentMapper;
import com.example.mapper.UserMapper;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 帖子业务层方法
 */
@Service
public class ArticleService {

    @Resource
    private ArticleMapper articleMapper;
    @Resource
    private UserMapper usermapper;
    @Resource
    private CommentMapper commentMapper;

    public void add(Article article) {
        Account currentUser = TokenUtils.getCurrentUser();
        article.setUserId(currentUser.getId());
        article.setTime(DateUtil.now());
        article.setStatus("待审核");
        articleMapper.insert(article);
    }

    public void updateById(Article article) {
        Account currentUser = TokenUtils.getCurrentUser();
        if (RoleEnum.USER.name().equals(currentUser.getRole()) ){
            article.setStatus("待审核");
        }
        articleMapper.updateById(article);
    }
    public void updateViews(Article article) {
        articleMapper.updateById(article);
    }

    public void deleteById(Integer id) {
        articleMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            articleMapper.deleteById(id);
        }
    }

    public Article selectById(Integer id) {
        Article article =articleMapper.selectById(id);
        User user=usermapper.selectById(article.getUserId());
        if (ObjectUtil.isNotEmpty(user)){
            article.setUserName(user.getName());
        }

        return article;
    }

    public List<Article> selectAll(Article article) {
       List<Article> list = articleMapper.selectAll(article);
       for (Article dbArticle:list){
           dbArticle.setContent(HtmlUtil.cleanHtmlTag(dbArticle.getContent()));
          List<Comment> comments = commentMapper.selectByArticleId(dbArticle.getId());
          dbArticle.setComment(comments.size());

       }
       return list;
    }

    public PageInfo<Article> selectPage(Article article, Integer pageNum, Integer pageSize) {
        Account currentUser = TokenUtils.getCurrentUser();
        if(RoleEnum.USER.name().equals(currentUser.getRole())){
            article.setUserId(currentUser.getId());
        }
        PageHelper.startPage(pageNum, pageSize);
        List<Article> list = articleMapper.selectAll(article);
        return PageInfo.of(list);
    }

}
