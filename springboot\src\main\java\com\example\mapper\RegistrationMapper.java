package com.example.mapper;

import com.example.entity.Registration;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RegistrationMapper {

    int insert(Registration registration);

    void updateById(Registration registration);

    void deleteById(Integer id);

    @Select("select * from `registration` where id = #{id}")
    Registration selectById(Integer id);

    List<Registration> selectAll(Registration registration);

}
