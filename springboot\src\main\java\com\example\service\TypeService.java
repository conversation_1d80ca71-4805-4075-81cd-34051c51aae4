package com.example.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.example.common.enums.RoleEnum;
import com.example.entity.Account;
import com.example.entity.Business;
import com.example.entity.Room;
import com.example.entity.Type;
import com.example.mapper.BusinessMapper;
import com.example.mapper.RoomMapper;
import com.example.mapper.TypeMapper;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 业务层方法
 */
@Service
public class TypeService {

    @Resource
    private TypeMapper typeMapper;
    @Resource
    private RoomMapper roomMapper;
    @Resource
    private BusinessMapper businessMapper;

    public void add(Type type) {
        Account currentUser = TokenUtils.getCurrentUser();
        type.setBusinessId(currentUser.getId());
        typeMapper.insert(type);
    }

    public void updateById(Type type) {
        typeMapper.updateById(type);
    }

    public void deleteById(Integer id) {
        typeMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            typeMapper.deleteById(id);
        }
    }

    public Type selectById(Integer id) {
        Type type = typeMapper.selectById(id);
        List<Room> rooms = roomMapper.selectByTypeId(type.getId());
        type.setTotal(rooms.size());
        type.setNum(rooms.stream().filter(x -> "空闲".equals(x.getStatus())).count());
        return type;
    }

    public List<Type> selectAll(Type type) {

        List<Type> types = typeMapper.selectAll(type);
        for(Type dbType : types){
            List<Room> rooms = roomMapper.selectByTypeId(dbType.getId());
            dbType.setTotal(rooms.size());
            dbType.setNum(rooms.stream().filter(x -> "空闲".equals(x.getStatus())).count());
        }
        return types;
    }

    public PageInfo<Type> selectPage(Type type, Integer pageNum, Integer pageSize) {
        Account currentUser = TokenUtils.getCurrentUser();
        if(RoleEnum.BUSINESS.name().equals(currentUser.getRole())){
            type.setBusinessId(currentUser.getId());
        }
        PageHelper.startPage(pageNum, pageSize);
        List<Type> list = typeMapper.selectAll(type);
        for(Type dbType : list){
            List<Room> rooms = roomMapper.selectByTypeId(dbType.getId());
            dbType.setTotal(rooms.size());
            dbType.setNum(rooms.stream().filter(x -> "空闲".equals(x.getStatus())).count());
        }
        return PageInfo.of(list);
    }

    public Map<String, Object> selectData(Integer typeId) {
        Map<String,Object> map =new HashMap<>();
        Type type =typeMapper.selectById(typeId);
        Business business = businessMapper.selectById(type.getBusinessId());
        if(ObjectUtil.isNotEmpty(business)){
            map.put("businessData",business);
            Type dbType =new Type();
            dbType.setBusinessId(business.getId());
            List<Type> types = typeMapper.selectAll(dbType);
            List<Type> extraTypes = types.stream().filter(x -> !x.getId().equals(typeId)).collect(Collectors.toList());
            map.put("extraTypeData",extraTypes);
        }
        return map;
    }
}
