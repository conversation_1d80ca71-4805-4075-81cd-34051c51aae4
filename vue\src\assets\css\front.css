.front-notice {
    padding: 5px 20px;
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
}

.front-header {
    display: flex;
    height: 60px;
    line-height: 60px;
    border-bottom: 1px solid #eee;
    background-color:lightskyblue;
}

.front-header-left {
    width: 350px;
    display: flex;
    align-items: center;
    padding-left: 30px;
}

.front-header-left img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.front-header-left .title {
    color: white;
    cursor: pointer;
    margin-left: 10px;
    font-size: 20px;
    font-weight: bold;
}

.front-header-center {
    flex: 1;
}

.front-header-right {
    padding-right: 20px;
}

.main-content {
    width: 80%;
    margin: 5px auto;
}

/*Element-Plus样式覆盖*/
.el-menu--horizontal {
    border: none !important;
    height: 59px;
}
.el-tooltip__trigger {
    outline: none;
}
.el-menu{
    background-color:lightskyblue;
}
.el-menu-item{
    background-color:lightskyblue !important;
    color:white !important;
}
.el-menu-item.is-active{
    background-color:lightskyblue !important;
    color:#fcfcfd !important;
    font-weight: 550;
    border-bottom: none;
}