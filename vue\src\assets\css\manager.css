.manager-container {
    background-color: #f8f8ff;
    min-height: 100vh;
}

.manager-header {
    height: 60px;
    background-color: #006eff;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
}

.manager-header-left {
    background-color: #006eff;
    width: 200px;
    height: 100%;
    padding-left: 10px;
    display: flex;
    align-items: center
}

.manager-header-left img {
    width: 30px;
    height: 30px;
}

.manager-header-left .title {
    font-weight: bold;
    font-size: 20px;
    margin-left: 10px;
    color: white;
}

.manager-header-center {
    flex: 1;
    padding-left: 20px;
}

.manager-main-left {
    width: 200px;
    background-color: #fff;
    min-height: calc(100vh - 60px);
}

.manager-main-right {
    flex: 1;
    width: 0;
    background-color: #f8f8ff;
    padding: 10px;
}


/* ElementUI */
.el-menu {
    border: none;
    background-color: white;
}

.el-tooltip__trigger {
    outline: none;
}

/deep/.el-sub-menu__title {
    color: #666 !important;
    height: 50px;
    line-height: 50px;
    background-color: #fff !important;
}

.el-sub-menu__title:hover {
    background-color: #fff; /* 必须加上这个背景色，否则鼠标移入会出现白色 */
    color: #fff;
}

.el-menu-item {
    color: #666;
    height: 50px;
    line-height: 50px;
    background-color: #fff !important; /* 必须加上这个背景色，否则鼠标离开会出现白色 */
}

.el-menu--inline .el-menu-item {
    background-color: #fff !important;
}

.el-menu-item.is-active {
    background-color: #cce2ff !important;
    color: #006eff;
    border-right: 3px solid #006eff;
}

.el-menu-item:not(.is-active):hover {
    color: #006eff;
}
.el-menu-item:hover {
    background-color: #e6f1ff !important;
    color: #006eff;
}

/deep/.el-breadcrumb__inner {
    color: white !important;
}

/deep/.manager-header-right span {
    color: white;
}

th.el-table__cell {
    background-color: #f8f8f8 !important;
    color: #666;
}