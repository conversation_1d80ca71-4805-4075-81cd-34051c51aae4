package com.example.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.example.common.enums.RoleEnum;
import com.example.entity.Account;
import com.example.entity.BusinessOrders;
import com.example.entity.Registration;
import com.example.entity.Room;
import com.example.exception.CustomException;
import com.example.mapper.BusinessOrdersMapper;
import com.example.mapper.RegistrationMapper;
import com.example.mapper.RoomMapper;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层方法
 */
@Service
public class RegistrationService {

    @Resource
    private RegistrationMapper registrationMapper;
    @Resource
    private BusinessOrdersMapper businessOrdersMapper;
    @Resource
    private RoomMapper roomMapper;

    public void add(Registration registration) {
         BusinessOrders businessOrders = businessOrdersMapper.selectById(registration.getOrderId());
        //判断时间是否有效,前端直接传入数据
        DateTime inTime = DateUtil.parse(registration.getInTime());
        DateTime startTime =DateUtil.parse(businessOrders.getStart());
        DateTime endTime = DateUtil.parse(businessOrders.getEnd());
        if(inTime.isBefore(startTime) || inTime.isAfter(endTime)){
            throw new CustomException("500","选择日期不合法");
        }
        registrationMapper.insert(registration);
        //更新订单状态
        businessOrders.setStatus("已入住");
        businessOrdersMapper.updateById(businessOrders);
        //更新对应房间状态
        Room room = roomMapper.selectById(registration.getRoomId());
        if(ObjectUtil.isNotEmpty(room)){
            room.setStatus("使用中");
            roomMapper.updateById(room);
        }
    }

    public void updateById(Registration registration) {
        registration.setOutTime(DateUtil.now());
        registrationMapper.updateById(registration);
        //更新订单状态
       BusinessOrders businessOrders = businessOrdersMapper.selectByOrderNo(registration.getOrderNo());
       if(ObjectUtil.isNotEmpty(businessOrders)){
           businessOrders.setStatus("已退房");
           businessOrdersMapper.updateById(businessOrders);
       }
       //更新房间状态
       Room room = roomMapper.selectById(registration.getRoomId());
        if(ObjectUtil.isNotEmpty(room)){
            room.setStatus("空闲");
            roomMapper.updateById(room);
        }
    }

    public void deleteById(Integer id) {
        registrationMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            registrationMapper.deleteById(id);
        }
    }

    public Registration selectById(Integer id) {
        return registrationMapper.selectById(id);
    }

    public List<Registration> selectAll(Registration registration) {
        return registrationMapper.selectAll(registration);
    }

    public PageInfo<Registration> selectPage(Registration registration, Integer pageNum, Integer pageSize) {
        Account currentUser = TokenUtils.getCurrentUser();
        if (RoleEnum.BUSINESS.name().equals(currentUser.getRole())) {
            registration.setBusinessId(currentUser.getId());
        }
        PageHelper.startPage(pageNum, pageSize);
        List<Registration> list = registrationMapper.selectAll(registration);
        return PageInfo.of(list);
    }

}
