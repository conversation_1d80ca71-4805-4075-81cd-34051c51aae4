<template>
  <div  style="width:80%;margin:20px auto;min-height:600px">
  <div style="display: flex;grid-gap:10px">
    <div style="flex:1;">
      <div class="card" style="display:flex;grid-gap:20px" >
        <img :src="data.sceneryData.img" alt="" style="height:240px;width: 300px; border-radius:5px">
        <div>
          <div style="font-size: 18px">{{data.sceneryData.name}}</div>
          <div style="margin-top:15px;font-size: 15px;color:#666">所属省份；<el-tag type="info">{{data.sceneryData.provinceName}}</el-tag></div>
          <div style="margin-top:15px;font-size: 15px;color:#666;display:flex; align-items:center">
            <div>推荐指数；</div>
            <el-rate v-model="data.sceneryData.recommend" disabled></el-rate>
          </div>
          <div style="margin-top:20px;font-size:15px; line-weight:30px">
            简介；{{data.sceneryData.description}}
          </div>
        </div>
      </div>
      <div class="card" style="margin-top: 10px;padding:0 20px" v-html="data.sceneryData.content" ></div>
    </div>
    <div style="width:400px">
      <div class="card" style="font-size:20px; font-weight:bold;">景点附近宾馆 ({{data.businessData.length}})</div>
      <div style="margin-top:10px">
        <div class="card" v-for="item in data.businessData">
          <img :src="item.img" alt="" style="width: 100%; height:200px;border-radius:5px;cursor:pointer" @click="navTo('/front/businessDetail?id=' + item.id)">
          <div style="font-size:16px;margin-top: 10px">{{item.name}}</div>
        </div>
      </div>
    </div>
  </div>
  </div>
</template>
<script setup>
import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
import router from "@/router/index.js";
const data = reactive({
 sceneryId:router.currentRoute.value.query.id,
  sceneryData:{},
  businessData:[]
})
const loadScenery = () =>{
  request.get('/scenery/selectById/'+data.sceneryId).then(res =>{
    if(res.code === '200'){
      data.sceneryData = res.data
      loadBusiness(data.sceneryData.provinceId)
      updateViews()
    }else{
      ElMessage.error(res.msg)
    }
  })
}
const updateViews = () =>{
  data.sceneryData.views =data.sceneryData.views +1
  request.put('/scenery/update',data.sceneryData).then(res =>{
    if(res.code !== '200'){
      ElMessage.error(res.msg)
    }
  })
}
loadScenery()
const loadBusiness = (provinceId) => {
  request.get('/business/selectAll', {
    params: {
      provinceId: provinceId
    }
  }).then(res => {
    if (res.code === '200') {
      data.businessData = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const navTo = (url) =>{
  location.href =url
}
</script>