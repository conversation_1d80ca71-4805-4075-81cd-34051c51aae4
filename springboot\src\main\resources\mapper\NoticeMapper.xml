<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.NoticeMapper">

    <select id="selectAll" resultType="com.example.entity.Notice">
        select * from `notice`
        <where>
            <if test="title != null"> and title like concat('%', #{title}, '%')</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `notice`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Notice" useGeneratedKeys="true">
        insert into `notice`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="time != null">time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="time != null">#{time},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Notice">
        update `notice`
        <set>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="time != null">
                time = #{time},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>