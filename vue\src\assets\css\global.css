* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    color: #333;
    font-size: 14px;
}

a {
    text-decoration: none;
}

.card {
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,.1);
}
.front_card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,.1);
}
.line4 {
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    overflow: hidden;
}
.line2 {
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}
.line1 {
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
}