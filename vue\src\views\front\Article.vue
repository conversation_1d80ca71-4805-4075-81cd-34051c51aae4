<template>
  <div  style="width:60%;margin:20px auto;min-height:600px">
    <div class="card" style="display:flex">
      <div style="font-size: 20px; flex:1">旅游相关帖子</div>
      <div><el-button type="success" @click="handleAdd">发帖</el-button></div>
    </div>
    <div style="margin-top:10px; display:flex;grid-gap:20px" class="card" v-for="item in data.articleData">
      <img :src="item.img" alt="" style="width:200px;height:150px;border-radius:5px">
      <div>
        <div style="font-size:18px">{{item.title}}</div>
        <div @click="navTo('/front/articleDetail?id=' + item.id)" class="line4" style="color: #74726b; margin-top:10px;cursor:pointer">{{item.content}}</div>
        <div style="margin-top:20px;display:flex; align-items:center;">
          <img :src="item.userAvatar" alt="" style="width:20px;height:20px;border-radius:50%">
          <div style="margin-left:5px;margin-right:20px;color: #74726b;">{{item.userName}}</div>
          <el-icon size="15"><View /></el-icon>
          <div style="margin-left:5px;margin-right:20px;color: #74726b;">{{item.views}}</div>
          <el-icon size="15"><ChatDotRound /></el-icon>
          <div style="margin-left:5px;margin-right:20px" >{{ item.comment }}</div>
        </div>
      </div>
    </div>
    <el-dialog title="帖子信息" v-model="data.formVisible" width="50%" destroy-on-close>
      <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="110px" style="padding: 20px">
        <el-form-item prop="img" label="封面">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleImgUpload"
              list-type="picture"
          >
            <el-button type="primary">上传封面</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="title" label="帖子名称">
          <el-input v-model="data.form.title" placeholder="请输入帖子名称"></el-input>
        </el-form-item>
        <el-form-item prop="content" label="游玩详细介绍">
          <div style="border: 1px solid #ccc; width: 100%">
            <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editor="editorRef"
                :mode="mode"
            />
            <Editor
                style="height: 500px; overflow-y: hidden;"
                v-model="data.form.content"
                :mode="mode"
                :defaultConfig="editorConfig"
                @onCreated="handleCreated"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="save">确 定</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>
<script setup>
import {onBeforeUnmount, reactive, ref, shallowRef} from "vue";
import '@wangeditor/editor/dist/css/style.css';
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
const baseUrl = import.meta.env.VITE_BASE_URL
const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
  formVisible: false,
  articleData:[],
  form: {},
  rules: {
    title: [
      {required: true, message: '请输入帖子名称', trigger: 'blur'},
    ],
    content: [
      {required: true, message: '请输入帖子详情', trigger: 'blur'},
    ],
  }
})
const formRef =ref()
/* wangEditor5 初始化开始 */
const editorRef = shallowRef()  // 编辑器实例，必须用 shallowRef
const mode = 'default'
const editorConfig = { MENU_CONF: {} }
// 图片上传配置
editorConfig.MENU_CONF['uploadImage'] = {
  headers: {
    token: data.user.token,
  },
  server: baseUrl + '/files/wang/upload',  // 服务端图片上传接口
  fieldName: 'file'  // 服务端图片上传接口参数
}
// 组件销毁时，也及时销毁编辑器，否则可能会造成内存泄漏
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})
// 记录 editor 实例，重要！
const handleCreated = (editor) => {
  editorRef.value = editor
}
/* wangEditor5 初始化结束 */
const handleImgUpload = (res) =>{
    data.form.img = res.data
}
const handleAdd = () =>{
  data.form = {}
  data.formVisible = true
}
const save = () =>{
  formRef.value.validate(valid =>{
    if (valid){
      request.post('/article/add',data.form).then(res => {
        if (res.code === '200'){
          ElMessage.success('发布成功，等待管理员审核')
          data.formVisible =false
        } else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}
const loadArticles = () => {
  request.get('/article/selectAll',{
    params:{
      status: '通过'
    }
  }).then(res =>{
    if(res.code ==='200'){
      data.articleData = res.data
    }
  })
}
const navTo = (url) =>{
  location.href = url
}
loadArticles()

</script>