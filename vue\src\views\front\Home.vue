<template>
  <div style="min-height:600px">
    <el-carousel :interval="4000" type="card" height="300px">
      <el-carousel-item v-for="item in data.businessData" :key="item.id">
       <img :src="item.img" alt="" style="width:100%; height:300px" @click="navTo('/front/businessDetail?id=' + item.id)">
      </el-carousel-item>
    </el-carousel>
    <div style="width:80%;display: flex;margin:20px auto;grid-gap:40px">
      <div style="width:400px;">
        <div style="display:flex;align-items:center; border-bottom: 1px solid #ccc;padding-bottom:10px">
          <div style="flex:1;font-size:20px; font-weight:bold;color: #35ab3d">相关宾馆</div>
          <div style="width:120px;color:#666;font-size:15px;text-align:right;cursor:pointer"@click="navTo('/front/business')">查看更多宾馆 ></div>
        </div>
        <div style="margin-top:10px">
          <div v-for="item in data.leftBusinessData" class="front_card" style="margin-bottom:20px">
            <img :src="item.img" alt="" style="width:100%; height:220px; border-radius:5px;cursor:pointer" @click="navTo('/front/businessDetail?id=' + item.id)">
            <div style="padding:10px">
              <div style="display:flex;align-items: center">
                <div style="flex:1;font-size:18px;font-weight:bold;">{{item.name}}</div>
                <div style="display:flex;width:70px;align-items: center">
                  <el-icon size="18"><LocationInformation /></el-icon>
                  <div style="font-size: 16px">{{ item.provinceName }}</div>
                </div>
              </div>
              <div style="margin-top:10px;color:red; font-size:16px">
                <span style="font-weight:bold;font-size:22px">￥{{item.price}} </span> 起
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="flex:1">
        <div style="display:flex;align-items:center; border-bottom: 1px solid #ccc;padding-bottom:10px">
          <div style="flex:1;font-size:20px; font-weight:bold;color: #35ab3d">当地景点</div>
          <div style="width:120px;color:#666;font-size:15px;text-align:right; cursor:pointer" @click="navTo('/front/scenery')">查看更多 ></div>
        </div>
        <div style="margin-top:10px">
           <el-row :gutter="20">
              <el-col :span="8" v-for="item in data.sceneryData" style="margin-bottom: 20px" class="front_card">
                <img :src="item.img" alt="" style="width:100%; height:220px; border-radius:5px;cursor:pointer" @click="navTo('/front/sceneryDetail?id=' + item.id)">
                <div style="padding:10px" >
                  <div style="display:flex;align-items: center">
                    <div class="line1" style="flex:1;font-size:18px;font-weight:bold;">{{item.name}}</div>
                    <div style="display:flex;width:70px;align-items: center">
                      <el-icon size="18"><LocationInformation /></el-icon>
                      <div style="font-size: 16px">{{ item.provinceName }}</div>
                    </div>
                  </div>
                </div>
              </el-col>
           </el-row>
        </div>
        <div style="margin-top:20px;display:flex;align-items:center; border-bottom: 1px solid #ccc;padding-bottom:10px">
          <div style="flex:1;font-size:20px; font-weight:bold;color: #35ab3d">旅游帖子</div>
          <div style="width:120px;color:#666;font-size:15px;text-align:right; cursor:pointer" @click="navTo('/front/article')">查看更多 ></div>
        </div>
        <div style="margin-top:10px">
          <div v-for="item in data.articleData" class="card" style="margin-bottom:10px;display:flex;grid-gap:20px">
            <img :src="item.img" alt="" style="width:200px;height:150px;border-radius:5px">
            <div>
              <div style="font-size:18px">{{item.title}}</div>
              <div @click="navTo('/front/articleDetail?id=' + item.id)" class="line4" style="color: #74726b; margin-top:10px;cursor:pointer">{{item.content}}</div>
              <div style="margin-top:20px;display:flex; align-items:center;">
                <img :src="item.userAvatar" alt="" style="width:20px;height:20px;border-radius:50%">
                <div style="margin-left:5px;margin-right:20px;color: #74726b;">{{item.userName}}</div>
                <el-icon size="15"><View /></el-icon>
                <div style="margin-left:5px;margin-right:20px;color: #74726b;">{{item.views}}</div>
                <el-icon size="15"><ChatDotRound /></el-icon>
                <div style="margin-left:5px;margin-right:20px" >{{item.comment}}</div>
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>
<script setup>

import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";
const data = reactive({
  businessData: [],
  leftBusinessData: [],
  sceneryData:[],
  articleData:[],
})
const loadBusiness = () =>{
  request.get('/business/selectAll',{
    params:{
      status: '通过'
    }
  }).then(res => {
    if(res.code === '200'){
      data.businessData = res.data
      if(data.businessData.length > 4){
        data.leftBusinessData =data.businessData.slice(0,3)
      }else{
        data.leftBusinessData =data.businessData
      }
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadBusiness()
const loadScenery = () =>{
  request.get('/scenery/selectAll').then(res =>{
    if(res.code === '200'){
      data.sceneryData = res.data
      if(data.sceneryData.length > 6){
        data.sceneryData = data.sceneryData.slice(0,6)
      }
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadScenery()
const loadArticle = () =>{
  request.get('/article/selectAll').then(res =>{
    if(res.code === '200'){
      data.articleData = res.data
      if(data.articleData.length > 2){
        data.articleData = data.articleData.slice(0,2)
      }
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadArticle()
const navTo = (url) =>{
  location.href = url
}
</script>