<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.ProductMapper">

    <select id="selectAll" resultType="com.example.entity.Product">
        select product.*,province.name as provinceName from `product`
        left join province on product.province_id = province.id
        <where>
            <if test="name != null"> and product.name like concat('%', #{name}, '%')</if>
            <if test="provinceId != null"> and product.province_id = #{provinceId}</if>
        </where>
        order by id
    </select>

    <delete id="deleteById">
        delete from `product`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Product" useGeneratedKeys="true">
        insert into `product`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="unit != null">unit,</if>
            <if test="description != null">description,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="price != null">price,</if>
            <if test="num != null">num,</if>
            <if test="img != null">img,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="unit != null">#{unit},</if>
            <if test="description != null">#{description},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="price != null">#{price},</if>
            <if test="num != null">#{num},</if>
            <if test="img != null">#{img},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Product">
        update `product`
        <set>
            <if test="name != null">
               name = #{name},
            </if>
            <if test="unit != null">
                unit = #{unit},
            </if>
            <if test="img != null">
               img = #{img},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="num != null">
                num = #{num},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>