<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.UserMapper">

    <select id="selectAll" resultType="com.example.entity.User">
        select * from `user`
        <where>
            <if test="name != null"> and name like concat('%', #{name}, '%')</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `user`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.User" useGeneratedKeys="true">
        insert into `user`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="username != null">username,</if>
            <if test="password != null">password,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="avatar != null">avatar,</if>
            <if test="role != null">role,</if>
            <if test="account != null">account,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="username != null">#{username},</if>
            <if test="password != null">#{password},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="role != null">#{role},</if>
            <if test="account != null">#{account},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.User">
        update `user`
        <set>
            <if test="username != null">
                username = #{username},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="avatar != null">
                avatar = #{avatar},
            </if>
            <if test="role != null">
                role = #{role},
            </if>
            <if test="account != null">
                account = #{account},
            </if>
        </set>
        where id = #{id}
    </update>


</mapper>