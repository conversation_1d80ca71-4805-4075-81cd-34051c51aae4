<template>
  <div>
    <div class="card" style="margin-bottom: 5px">
      <el-input v-model="data.title" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输入帖子名称查询"></el-input>
      <el-button type="info" plain @click="load">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div class="card" style="margin-bottom: 5px">
      <el-button type="danger" plain @click="delBatch">批量删除</el-button>
    </div>
    <div class="card" style="margin-bottom: 5px">
      <el-table stripe :data="data.tableData" @selection-change="handleSelectionChange" tooltip-effect="light myEffect">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="img" label="帖子封面" width="80" >
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 5px; display: block" v-if="scope.row.img"
                      :src="scope.row.img" :preview-src-list="[scope.row.img]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="帖子标题" show-overflow-tooltip />
        <el-table-column prop="content" label="帖子内容"  >
          <template v-slot="scope">
            <el-button type="primary" @click="view(scope.row.content)">查看详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="userName" label="发布人"  />
        <el-table-column prop="time" label="发布时间"  />
        <el-table-column prop="views" label="浏览量"  />
        <el-table-column prop="status" label="帖子状态">
          <template v-slot="scope">
            <el-tag  v-if="scope.row.status ==='待审核'" type="warning">{{scope.row.status}}</el-tag>
            <el-tag  v-if="scope.row.status ==='通过'"  type="success">{{scope.row.status}}</el-tag>
            <el-tag  v-if="scope.row.status ==='拒绝'"  type="danger">{{scope.row.status}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template v-slot="scope">
            <el-button type="primary"@click="handleEdit(scope.row)">审批</el-button>
            <el-button type="danger" circle :icon="Delete" @click="del(scope.row.id)"></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="card" v-if="data.total">
      <el-pagination @current-change="load" background layout="prev, pager, next" :page-size="data.pageSize" v-model:current-page="data.pageNum" :total="data.total" />
    </div>

    <el-dialog title="审核信息" v-model="data.formVisible" width="50%" destroy-on-close>
      <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="100px" style="padding: 20px">
        <el-form-item prop="status" label="帖子状态">
          <el-radio-group v-model="data.form.status" fill="#A3A6AD" >
            <el-radio-button label="通过" value="通过" />
            <el-radio-button label="拒绝" value="拒绝" />
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="save">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="详细介绍" v-model="data.viewVisible" width="50%" destroy-on-close>
      <div v-html="data.viewContent" style="padding: 20px"></div>
    </el-dialog>
  </div>
</template>

<script setup>

import {onBeforeUnmount, reactive, ref, shallowRef} from "vue";
import '@wangeditor/editor/dist/css/style.css';
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

const baseUrl = import.meta.env.VITE_BASE_URL
const formRef = ref()
const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
  formVisible: false,
  form: {},
  tableData: [],
  provinceData:[],
  pageNum: 1,
  pageSize: 5,
  total: 0,
  name: null,
  ids: [],
  viewContent:null,
  viewVisible:null,
})
const load = () => {
  request.get('/article/selectPage', {
    params: {
      pageNum: data.pageNum,
      pageSize: data.pageSize,
      title: data.title
    }
  }).then(res => {
    if (res.code === '200') {
      data.tableData = res.data?.list || []
      data.total = res.data?.total
    }
  })
}
const handleEdit = (row) => {
  data.form = JSON.parse(JSON.stringify(row))
  data.formVisible = true
}
const add = () => {
  request.post('/article/add', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const view = (content) => {
  data.viewContent = content
  data.viewVisible = true
}
const update = () => {
  request.put('/article/update', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    }
  })
}

const save = () => {
  update()
}

const del = (id) => {
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete('/article/delete/' + id).then(res => {
      if (res.code === '200') {
        ElMessage.success("删除成功")
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const delBatch = () => {
  if (!data.ids.length) {
    ElMessage.warning("请选择数据")
    return
  }
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete("/article/delete/batch", {data: data.ids}).then(res => {
      if (res.code === '200') {
        ElMessage.success('操作成功')
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const handleSelectionChange = (rows) => {
  data.ids = rows.map(v => v.id)
}

const reset = () => {
  data.title = null
  load()
}
load()
</script>
<style>
.myEffect{
  width:40%
}
</style>