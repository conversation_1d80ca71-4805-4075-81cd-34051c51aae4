<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.TypeMapper">

    <select id="selectAll" resultType="com.example.entity.Type">
        select type.*,business.name as businessName from `type`
        left join business on type.business_id = business.id
        <where>
            <if test="name != null"> and type.name like concat('%', #{name}, '%')</if>
            <if test="businessId != null"> and type.business_id = #{businessId}</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `type`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Type" useGeneratedKeys="true">
        insert into `type`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="content != null">content,</if>
            <if test="description != null">description,</if>
            <if test="price != null">price,</if>
            <if test="img != null">img,</if>
            <if test="img1 != null">img1,</if>
            <if test="img2 != null">img2,</if>
            <if test="img3 != null">img3,</if>
            <if test="img4 != null">img4,</if>
            <if test="businessId != null">business_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="content != null">#{content},</if>
            <if test="description != null">#{description},</if>
            <if test="price != null">#{price},</if>
            <if test="img != null">#{img},</if>
            <if test="img1 != null">#{img1},</if>
            <if test="img2 != null">#{img2},</if>
            <if test="img3 != null">#{img3},</if>
            <if test="img4 != null">#{img4},</if>
            <if test="businessId != null">#{businessId},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Type">
        update `type`
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="img != null">
                img = #{img},
            </if>
            <if test="img1 != null">
                img1 = #{img1},
            </if>
            <if test="img2 != null">
                img2 = #{img2},
            </if>
            <if test="img3 != null">
                img3 = #{img3},
            </if>
            <if test="img4 != null">
                img4 = #{img4},
            </if>
            <if test="businessId != null">
                business_id = #{businessId},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>