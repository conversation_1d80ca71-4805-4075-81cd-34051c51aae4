<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.BusinessMapper">

    <select id="selectAll" resultType="com.example.entity.Business">
        select business.*,province.name as provinceName from `business`
        left join province on business.province_id = province.id
        <where>
            <if test="name != null"> and business.name like concat('%', #{name}, '%')</if>
            <if test="provinceId != null"> and province_id = #{provinceId}</if>
            <if test="status!= null"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectById" resultType="com.example.entity.Business">
        select * from `business`
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from `business`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Business" useGeneratedKeys="true">
        insert into `business`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="username != null">username,</if>
            <if test="password != null">password,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="avatar != null">avatar,</if>
            <if test="role != null">role,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="address != null">address,</if>
            <if test="content != null">content,</if>
            <if test="price != null">price,</if>
            <if test="link != null">link,</if>
            <if test="img != null">img,</if>
            <if test="license != null">license,</if>
            <if test="leader != null">leader,</if>
            <if test="code != null">code,</if>
            <if test="front != null">front,</if>
            <if test="back != null">back,</if>
            <if test="status != null">status,</if>
            <if test="score!= null">score,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="username != null">#{username},</if>
            <if test="password != null">#{password},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="role != null">#{role},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="address != null">#{address},</if>
            <if test="content != null">#{content},</if>
            <if test="price != null">#{price},</if>
            <if test="link != null">#{link},</if>
            <if test="img != null">#{img},</if>
            <if test="license != null">#{license},</if>
            <if test="leader != null">#{leader},</if>
            <if test="code != null">#{code},</if>
            <if test="front != null">#{front},</if>
            <if test="back != null">#{back},</if>
            <if test="status != null">#{status},</if>
            <if test="score != null">#{score},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Business">
        update `business`
        <set>
            <if test="username != null">
                username = #{username},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="avatar != null">
                avatar = #{avatar},
            </if>
            <if test="role != null">
                role = #{role},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="link != null">
                link = #{link},
            </if>
            <if test="img != null">
                img = #{img},
            </if>
            <if test="license != null">
                license = #{license},
            </if>
            <if test="leader != null">
                leader = #{leader},
            </if>
            <if test="code != null">
                code = #{code},
            </if>
            <if test="front != null">
                front = #{front},
            </if>
            <if test="back != null">
                back = #{back},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="score != null">
                score = #{score},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>