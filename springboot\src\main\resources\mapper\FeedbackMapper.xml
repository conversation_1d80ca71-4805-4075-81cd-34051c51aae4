<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.FeedbackMapper">

    <select id="selectAll" resultType="com.example.entity.Feedback">
        select feedback.*,user.name as username from `feedback`
        left join user on feedback.user_id = user.id
        <where>
            <if test="title != null"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="userId != null"> and user_id = #{userId}</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `feedback`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Feedback" useGeneratedKeys="true">
        insert into `feedback`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="question != null">question,</if>
            <if test="idea != null">idea,</if>
            <if test="time != null">time,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="question != null">#{question},</if>
            <if test="idea != null">#{idea},</if>
            <if test="time != null">#{time},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status!= null">#{status},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Feedback">
        update `feedback`
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="content != null">
                content = #{content},
            </if>
            <if test="replyTime != null">
                reply_time = #{replyTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>