<template>
  <div  style="width:60%;margin:20px auto;min-height:600px">
    <div>
      <el-input v-model="data.name" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输入景点名称查询">
      </el-input>
      <el-select v-model="data.provinceId" placeholder="请选择省份" style="width:240px;margin:0 10px">
        <el-option
            v-for="item in data.provinceData"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        />
      </el-select>
      <el-button type="info" plain @click="loadScenery">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div style="margin-top:20px">
      <el-row gutter="10">
        <el-col :span="12" v-for="item in data.sceneryData" style="margin-bottom: 10px">
          <div class="card" style="display: flex;grid-gap:10px;">
            <img :src="item.img" alt=“” style="width:200px;height:120px; border-radius: 5px; cursor: pointer" @click="navTo('/front/sceneryDetail?id='+item.id)">
            <div style="color:#74726b">
              <div style="font-size:18px;display:flex;align-items:center;grid-gap:10px">
                <el-icon size="20"><OfficeBuilding /></el-icon>
                <div>景点名称：{{item.name}}</div>
              </div>
              <div style="display: flex; align-items: center; grid-gap: 5px; margin-top: 10px">
                <el-icon><LocationInformation /></el-icon>
                <div>所属省份：{{ item.provinceName }}</div>
              </div>
              <div style="display: flex; align-items: center; grid-gap: 5px; margin-top: 10px">
                <el-icon><Apple /></el-icon>
                <div>浏览量：{{ item.views }}</div>
              </div>
              <div style="display: flex; align-items: center; grid-gap: 5px; margin-top: 10px">
                <el-icon><Opportunity /></el-icon>
                <div>推荐指数： </div>
                <el-rate v-model="item.recommend" disabled />
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script setup>
import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
const data = reactive({
  name:null,
  provinceId:null,
  provinceData:null,
  sceneryData:[]
})
const loadScenery = () => {
  request.get('/scenery/selectAll',{
    params: {
      name:data.name,
      provinceId:data.provinceId
    }
  }).then(res => {
    if (res.code === '200') {
      data.sceneryData = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
loadScenery()
const loadProvince = () =>{
  request.get('province/selectAll').then(res => {
    if(res.code === '200'){
      data.provinceData = res.data
    } else{
      ElMessage.error(res.msg)
    }
  })
}
loadProvince()
const reset =()=>{
  data.name=null
  data.provinceId=null
  loadScenery()
}
const navTo =(url) =>{
  location.href=url
}
</script>