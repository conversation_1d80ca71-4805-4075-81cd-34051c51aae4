<template>
  <div>
  <div style="display:flex;grid-gap:10px">
    <div class="card" style="flex:1 ;display:flex;align-items:center;padding:20px 0">
      <div style="flex:1 ;text-align:center">
        <img src="@/assets/imgs/余额.jpg" alt="" style="width:70px;height:70px;">
      </div>
      <div style="flex:1;font-size:20px">
        <div>宾馆总流水</div>
        <div style="font-weight:bold; color:red;">￥{{data.baseData.businessTotal}}</div>
      </div>
    </div>
    <div class="card" style="flex:1 ;display:flex;align-items:center;padding:20px 0">
      <div style="flex:1 ;text-align:center">
        <img src="@/assets/imgs/余额2.jpg" alt="" style="width:70px;height:70px;">
      </div>
      <div style="flex:1;font-size:20px">
        <div>特产总流水</div>
        <div style="font-weight:bold; color:red;">￥{{data.baseData.productTotal}}</div>
      </div>
    </div>
    <div class="card" style="flex:1 ;display:flex;align-items:center;padding:20px 0">
      <div style="flex:1 ;text-align:center">
        <img src="@/assets/imgs/房间.jpg" alt="" style="width:70px;height:70px;">
      </div>
      <div style="flex:1;font-size:20px">
        <div>宾馆数量</div>
        <div style="font-weight:bold">{{data.baseData.businessNum}}</div>
      </div>
    </div>
    <div class="card" style="flex:1 ;display:flex;align-items:center;padding:20px 0">
      <div style="flex:1 ;text-align:center">
        <img src="@/assets/imgs/特产2.png" alt="" style="width:70px;height:70px;">
      </div>
      <div style="flex:1;font-size:20px">
        <div>特产种类</div>
        <div style="font-weight:bold">{{data.baseData.productNum}}</div>
      </div>
    </div>
    <div class="card" style="flex:1 ;display:flex;align-items:center;padding:20px 0">
      <div style="flex:1 ;text-align:center">
        <img src="@/assets/imgs/景点.jpg" alt="" style="width:70px;height:70px;">
      </div>
      <div style="flex:1;font-size:20px">
        <div>热门景点</div>
        <div style="font-weight:bold">{{data.baseData.sceneryNum}}</div>
      </div>
    </div>
    <div class="card" style="flex:1 ;display:flex;align-items:center;padding:20px 0">
      <div style="flex:1 ;text-align:center">
        <img src="@/assets/imgs/帖子2.jpg" alt="" style="width:70px;height:70px;">
      </div>
      <div style="flex:1;font-size:20px">
        <div>帖子数量</div>
        <div style="font-weight:bold">{{data.baseData.articleNum}}</div>
      </div>
    </div>
  </div>
      <div style="margin-top: 10px; height: 500px" class="card" id="line"></div>
      <div style="margin-top:10px; display:flex;grid-gap:10px">
      <div style="flex:2;height:400px" class="card" id="pie"></div>
      <div style="flex:3;height:400px" class="card" id="bar"></div>
      </div>
  </div>
</template>

<script setup>

import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
import * as echarts from "echarts";
const data = reactive({
  baseData:{}
})
const loadBaseData = () =>{
  request.get('/statistics/base').then(res =>{
    if(res.code === '200'){
      data.baseData = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadBaseData()
const loadLine = () =>{
  request.get('/statistics/line').then(res =>{
    if(res.code === '200'){
       let chartDom = document.getElementById('line')
       let myChart = echarts.init(chartDom)
       lineOptions.xAxis.data = res.data.xList
       lineOptions.series[0].data = res.data.yList
       myChart.setOption(lineOptions)
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadLine()
//流水折线图
let lineOptions = {
  title: {
    text: '近一周平台每日宾馆流水折线图',
    subtext: '统计维度：最近一周',
    left: 'center'
  },
  legend: {
    data: [],
    template:""
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '20%',
    containLabel: true
  },
  tooltip: {
    trigger: 'item'
  },
  xAxis: {
    name: '日期',
    type: 'category',
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
  },
  yAxis: {
    name: '流水金额',
    type: 'value'
  },
  series: [
    {
      name: '流水金额',
      data: [820, 932, 901, 934, 1290, 1330, 1320],
      type: 'line',
      smooth: true,
      markLine: {
        data: [{ type: 'average', name: '最近7天宾馆预订流水金额平均值' }]
      },
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
    },
  ]
}
const loadPie = () =>{
  request.get('/statistics/pie').then(res =>{
    if(res.code === '200'){
      let chartDom = document.getElementById('pie')
      let myChart = echarts.init(chartDom)
      pieOptions.series[0].data = res.data
      myChart.setOption(pieOptions)
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadPie()
const loadBar = () =>{
  request.get('/statistics/bar').then(res =>{
    if(res.code === '200'){
      let chartDom = document.getElementById('bar')
      let myChart = echarts.init(chartDom)
      barOptions.xAxis.data = res.data.xList
      barOptions.series[0].data = res.data.yList
      myChart.setOption(barOptions)
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadBar()
let pieOptions = {
  title: {
    text: '不同省份宾馆数量分布饼状图', // 主标题
    subtext: '统计维度：省份信息', // 副标题
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '数量占比', // 鼠标移上去显示内容
      type: 'pie',
      radius: '50%',
      center: ['50%', '60%'],
      data: [
        {value: 1048, name: '瑞幸咖啡'}, // 示例数据：name表示维度，value表示对应的值
        {value: 735, name: '雀巢咖啡'},
        {value: 580, name: '星巴克咖啡'},
        {value: 484, name: '栖巢咖啡'},
        {value: 300, name: '小武哥咖啡'}
      ]
    }
  ]
}
let barOptions = {
  title: {
    text: '不同商家营业额柱状图', // 主标题
    subtext: '统计维度：宾馆名称', // 副标题
    left: 'center'
  },
  grid : {   // ---------------------------增加这个属性，bottom就是距离底部的距离
    bottom : '10%',
    top: '25%'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  xAxis: {
    type: 'category',
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'], // 示例数据：统计的维度（横坐标）
    name: '房间类型',
    axisLabel: {
      show: true,
      interval: 0,
      rotate: -60,
      inside: false,
      margin: 6,
    },
  },
  yAxis: {
    type: 'value',
    name: '空闲数量',
  },
  tooltip: {
    trigger: 'item',
  },
  series: [
    {
      data: [120, 200, 150, 80, 70, 110, 130], // 示例数据：横坐标维度对应的值（纵坐标）
      type: 'bar',
      itemStyle: {
        normal: {
          color: function () {
            return "#" + Math.floor(Math.random() * (256 * 256 * 256 - 1)).toString(16);
          }
        },
      },
    }
  ]
}
</script>