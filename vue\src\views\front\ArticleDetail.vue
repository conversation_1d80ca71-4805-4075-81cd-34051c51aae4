<template>
  <div  style="width:50%;margin:20px auto;min-height:600px">
    <div class="card" style="padding: 20px;">
      <div style="font-size:24px;font-weight:bold;text-align:center">{{data.articleData.title}}</div>
      <div style="margin-top: 15px;text-align:center;color:#666">
        <span>发布时间：{{data.articleData.time}}</span>
        <span style="margin: 0 20px">发布人：{{data.articleData.userName}}</span>
        <span>浏览量：{{data.articleData.views}}</span>
      </div>
      <div v-html="data.articleData.content"></div>
    </div>
    <div class="card" style="margin-top:10px;padding:20px" >
      <div style="font-size:18px;font-weight:bold">评论共 {{data.commentData.length}} 条</div>
      <div style="margin-top:20px">
        <el-input type="textarea" :rows="5" v-model="data.content" placeholder="请输入评论"></el-input>
      </div>
      <div style="margin-top:10px;text-align:right">
        <el-button type="primary" style="padding:15px 25px" @click="comment">评论</el-button>
      </div>
      <div style="margin-top:30px;display:flex;grid-gap:20px" v-for="item in data.commentData">
        <img :src="item.userAvatar" alt="" style="width:50px;height:50px;border-radius:50%">
        <div >
          <div style="display:flex;align-items: center ; grid-gap:5px;color:#666">
            <div>{{item.userName}}</div>
            <el-icon size="16"><User /></el-icon>
          </div>
          <div style="margin-top:10px;line-height:25px">{{item.content}}</div>
          <div style="margin-top:10px;display:flex;color:#666;align-items:center;">
            <div>{{item.time}}</div>
            <el-icon size="16" style="margin-left:20px;margin-right:5px"><Comment /></el-icon>
            <div v-if="data.user.id === item.userId" style="cursor:pointer" @click="del(item.id)">删除</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import router from "@/router/index.js";
const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
  articleId: router.currentRoute.value.query.id,
  articleData:{},
  content:null,
  commentData:[],
})
const loadArticle = () =>{
  request.get('/article/selectById/'+data.articleId).then(res =>{
    if(res.code === '200'){
      data.articleData = res.data
      data.articleData.views = data.articleData.views + 1
      request.put('/article/updateViews',data.articleData)
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadArticle()
const comment = () =>{
  if(!data.content){
    ElMessage.warning('请输入评论内容')
    return
  }
  request.post('/comment/add',{
      content:data.content,
      articleId:data.articleId

  }).then(res => {
    if(res.code === '200'){
      ElMessage.success('评论成功')
      data.content=null
      loadComment()
    }
  })
}
const del = (id) => {
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete('/comment/delete/' + id).then(res => {
      if (res.code === '200') {
        ElMessage.success("删除成功")
        loadComment()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const loadComment = () =>{
  request.get('/comment/selectAll',{
    params: {
      articleId:data.articleId
    }
  }).then(res =>{
    if (res.code === '200'){
      data.commentData = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadComment()
</script>