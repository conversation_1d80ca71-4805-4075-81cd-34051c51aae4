<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.RoomMapper">

    <select id="selectAll" resultType="com.example.entity.Room">
        select room.*, business.name as businessName,type.name as typeName from `room`
        left join business on room.business_id = business.id
        left join type on room.type_id = type.id
        <where>
            <if test="name != null"> and room.name like concat('%', #{name}, '%')</if>
            <if test="businessId != null"> and room.business_id =#{businessId}</if>
            <if test="typeId != null"> and room.type_id =#{typeId}</if>
            <if test="status != null"> and room.status =#{status}</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `room`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Room" useGeneratedKeys="true">
        insert into `room`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="typeId != null">type_id,</if>
            <if test="level != null">level,</if>
            <if test="businessId != null">business_id,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="level != null">#{level},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Room">
        update `room`
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="typeId != null">
                type_id = #{typeId},
            </if>
            <if test="level != null">
                level = #{level},
            </if>
            <if test="businessId != null">
                business_id = #{businessId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>