<template>
  <div  style="min-height:600px">
    <img :src="data.businessData.img" alt="" style="width:100%;height:550px">
    <div style="width:90%;margin:20px auto;display:flex;grid-gap:20px">
      <div style="width:400px;padding:0 20px">
        <div style="height:40px;line-height:40px;color:#666;font-size:20px;font-weight:bold;text-align:center;border-bottom:1px solid #cccc">其他类型房间</div>
        <div style="margin-top:20px;padding:0 20px" v-for="item in data.extraTypeData">
          <div class="front_card">
            <img :src="item.img" alt="" style="width:100%;height:200px;border-radius:5px 5px 0 0;cursor:pointer"@click="changeType(item.id)">
            <div style="line-height:40px;height:40px;text-align:center;">{{item.name}}</div>
          </div>
        </div>
      </div>
      <div style="flex:1;padding:20px">
       <div style="display:flex;grid-gap:40px">
         <div style="flex:3">
           <img :src="data.img" alt="" style="width:100%;height:350px;margin-top:20px">
           <div style="margin-top:5px;display:flex;grid-gap:10px">
             <img @click="clickImg(data.typeData.img1)" :src="data.typeData.img1" alt="" style="width:100px;height:100px;cursor:pointer">
             <img @click="clickImg(data.typeData.img2)" :src="data.typeData.img2" alt="" style="width:100px;height:100px;cursor:pointer">
           </div>
         </div>
         <div style="flex:2">
           <div style="font-size:18px;font-weight:bold;">基本信息</div>
           <div style="display:flex;align-items:center;font-size:16px;grid-gap:5px;margin-top:10px">
             <el-icon><CircleCheck /></el-icon>
             <div>类型：{{data.typeData.name}}</div>
           </div>
           <div style="display:flex;align-items:center;font-size:16px;grid-gap:5px;margin-top:10px">
             <el-icon><CircleCheck /></el-icon>
             <div>剩余间数：{{data.typeData.num}}</div>
           </div>
           <div style="display:flex;align-items:center;font-size:16px;grid-gap:5px;margin-top:10px">
             <el-icon><CircleCheck /></el-icon>
             <div>所属宾馆：{{data.businessData.name}}</div>
           </div>
           <div style="color:red; font-size:16px;margin-top:20px">
             价格：<span style="font-size: 20px;font-weight: bold">{{data.typeData.price}}</span> /晚
           </div>
           <div style="margin-top:20px">
             <el-button v-if="!data.flag" type="warning" @click="collect">收藏</el-button>
             <el-button v-if="data.flag" type="danger" @click="cancel">取消收藏</el-button>
             <el-button type="success" @click="reserveInit()" :disabled="data.typeData.num === 0">预订</el-button>
           </div>
         </div>
       </div>
        <div style="margin-top:30px;display:flex;grid-gap:10px">
          <div :class="{clickActive : data.flag1}" @click="clickChange('info')" style="height:40px; cursor:pointer;width:120px;line-height: 40px;box-shadow:0 0 10px #bdbdbd;text-align:center;border: 1px solid #e7e7e7;font-weight:bold;font-size:16px">房间详情</div>
          <div :class="{clickActive : data.flag2}" @click="clickChange('comment')" style="height:40px; cursor:pointer;width:120px;line-height: 40px;box-shadow:0 0 10px #bdbdbd;text-align:center;border: 1px solid #e7e7e7;font-weight:bold;font-size:16px">入住评论</div>
        </div>
        <div v-if="data.flag1" style="margin-top: 35px" v-html="data.typeData.content"></div>
        <div v-if="data.flag2" style="margin-top:35px">
          <div v-for="item in data.commentData" style="display:flex;grid-gap:20px;margin-bottom:30px">
            <div style="display:flex;grid-gap:5px">
              <img :src="item.userAvatar" alt="" style="width:40px;height:40px;border-radius:50%">
              <div>{{item.userName}}</div>
            </div>
            <div>
              <div>{{item.comment}}</div>
              <div style="margin-top:10px;color:#999">{{item.commentTime}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog title="预定房型信息" v-model="data.formVisible" width="40%" destroy-on-close>
      <el-form ref="formRef" :rules="data.rules" :model="data.form" label-width="90px" style="padding: 20px">
        <el-form-item prop="start" label="入住时间">
          <el-date-picker
              v-model="data.form.start"
              type="date"
              placeholder="请选择入住日期"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
          />
        </el-form-item>
        <el-form-item prop="end" label="离开时间">
          <el-date-picker
              v-model="data.form.end"
              type="date"
              placeholder="请选择离开日期"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="reserve">预 订</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import {reactive,ref} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
import router from "@/router/index.js";
const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
  img:null,
  typeId:router.currentRoute.value.query.id,
  businessData:{},
  typeData:{},
  extraTypeData:[],
  commentData:[],
  flag1:true,
  flag2:false,
  flag:false,
  formVisible:false,
  rules:{
    start: [
      { required: true, message: '请选择入住日期', trigger: 'blur' }
    ],
    end: [
      { required: true, message: '请选择离开日期', trigger: 'blur' }
    ],
  },
  form:{}
})
const disabledDate = (time) => {
  return time.getTime() < Date.now()
}
const reserveInit = () =>{
  data.form ={}
  data.formVisible = true
}
const formRef = ref()
const reserve = () =>{
  formRef.value.validate(valid =>{
    if(valid){
      data.form.typeId = data.typeId
      data.form.businessId = data.businessData.id
      request.post('businessOrders/add',data.form).then(res =>{
        if(res.code === '200'){
          ElMessage.success('预订成功,请支付订单')
          data.formVisible = false
        }else{
          ElMessage.error(res.msg)
        }
      })
    }
  })
}
const loadType = () =>{
  request.get('/type/selectById/' + data.typeId).then(res =>{
    if(res.code === '200'){
      data.typeData = res.data
      data.img = data.typeData.img
      //拆线呢对应宾馆信息，以及该宾馆除该房型外剩余房型
      request.get('/type/selectData/' + data.typeId).then(res =>{
       if(res.code === '200'){
         data.businessData =res.data.businessData
         data.extraTypeData = res.data.extraTypeData
       } else{
         ElMessage.error(res.msg)
       }
      })
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadType();
const clickChange = (type) =>{
  if('info' === type){
    data.flag1 = true
    data.flag2 = false
  }
  if('comment' === type){
    data.flag1 = false
    data.flag2 = true
  }
}
const clickImg = (url) =>{
  data.img = url
}
const changeType =(typeId) =>{
  data.typeId = typeId
  loadType()
  loadComments()
}
const collect =()=>{
  request.post('/collect/add',{
    typeId:data.typeId
  }).then(res =>{
    if (res.code ==='200'){
      ElMessage.success('收藏成功')
      loadCollect()
    }
  })
}
const cancel = () =>{
  request.get('/collect/deleteByUserId',{
    params:{
      userId:data.user.id,
      typeId:data.typeId
    }
  }).then(res =>{
    if(res.code === '200'){
      ElMessage.success('取消成功')
      loadCollect()
    } else{
      ElMessage.error(res.msg)
    }
  })
}
const loadCollect = () =>{
  request.get('collect/selectAll',{
    params: {
      userId:data.user.id,
      typeId:data.typeId
    }
  }).then(res =>{
    if(res.code === '200'){
      if(res.data.length >0){
        data.flag = true
      }else{
        data.flag = false
      }
    }
  })
}
loadCollect()
const loadComments = () =>{
  request.get('businessOrders/selectAll',{
    params:{
      typeId:data.typeId
    }
  }).then(res =>{
    if(res.code === '200'){
      data.commentData =res.data.filter(v =>v.comment !==null)
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadComments()
</script>
<style>
.clickActive{
  background: #000b17;
  cursor: pointer;
  height: 50px;
  line-height: 50px;
  box-shadow: 0 0 10px #bdbdbd;
  border: 1px solid #e7e7e7;
  width: 120px;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  color: white;
}
</style>