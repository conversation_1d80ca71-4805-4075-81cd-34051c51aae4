package com.example.mapper;

import com.example.entity.ProductOrders;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ProductOrdersMapper {

    int insert(ProductOrders productOrders);

    void updateById(ProductOrders productOrders);

    void deleteById(Integer id);

    @Select("select * from `product_orders` where id = #{id}")
    ProductOrders selectById(Integer id);

    List<ProductOrders> selectAll(ProductOrders productOrders);

}
