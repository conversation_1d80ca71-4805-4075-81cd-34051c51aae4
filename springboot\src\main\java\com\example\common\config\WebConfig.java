package com.example.common.config;

import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private JWTInterceptor jwtInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns("/", "/login", "/register")  // 排除登录注册和首页
                .excludePathPatterns("/files/**")  // 排除文件上传下载
                .excludePathPatterns("/assets/**", "/favicon.ico", "/index.html")  // 排除静态资源
                .excludePathPatterns("/*.js", "/*.css", "/*.html", "/*.ico", "/*.png", "/*.jpg", "/*.jpeg", "/*.gif")  // 排除静态文件
                .excludePathPatterns("/error");  // 排除错误页面
    }

}
