package com.example.controller;

import com.example.common.Result;
import com.example.entity.ProductOrders;
import com.example.service.ProductOrdersService;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 前端请求接口
 */
@RestController
@RequestMapping("/productOrders")
public class ProductOrdersController {

    @Resource
    private ProductOrdersService productOrdersService;

    /**
     * 新增
     */
    @PostMapping("/add")
    public Result add(@RequestBody ProductOrders productOrders) {
        productOrdersService.add(productOrders);
        return Result.success();
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    public Result update(@RequestBody ProductOrders productOrders) {
        productOrdersService.updateById(productOrders);
        return Result.success();
    }
    /**
     * 支付接口
     */
    @PutMapping("/pay")
    public Result pay(@RequestBody ProductOrders productOrders) {
        productOrdersService.pay(productOrders);
        return Result.success();
    }
    /**
     * 订单取消接口
     */
    @PutMapping("/cancel")
    public Result cancel(@RequestBody ProductOrders productOrders) {
        productOrdersService.cancel(productOrders);
        return Result.success();
    }

    /**
     * 单个删除
     */
    @DeleteMapping("/delete/{id}")
    public Result delete(@PathVariable Integer id) {
        productOrdersService.deleteById(id);
        return Result.success();
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/delete/batch")
    public Result delete(@RequestBody List<Integer> ids) {
        productOrdersService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 单个查询
     */
    @GetMapping("/selectById/{id}")
    public Result selectById(@PathVariable Integer id) {
        ProductOrders productOrders = productOrdersService.selectById(id);
        return Result.success(productOrders);
    }

    /**
     * 查询所有
     */
    @GetMapping("/selectAll")
    public Result selectAll(ProductOrders productOrders) {
        List<ProductOrders> list = productOrdersService.selectAll(productOrders);
        return Result.success(list);
    }

    /**
     * 分页查询
     */
    @GetMapping("/selectPage")
    public Result selectPage(ProductOrders productOrders,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<ProductOrders> pageInfo = productOrdersService.selectPage(productOrders, pageNum, pageSize);
        return Result.success(pageInfo);
    }

}
