<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.AdminMapper">

    <select id="selectAll" resultType="com.example.entity.Admin">
        select * from `admin`
        <where>
            <if test="name != null"> and name like concat('%', #{name}, '%')</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `admin`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Admin" useGeneratedKeys="true">
        insert into `admin`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="username != null">username,</if>
            <if test="password != null">password,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="avatar != null">avatar,</if>
            <if test="role != null">role,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="username != null">#{username},</if>
            <if test="password != null">#{password},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="role != null">#{role},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Admin">
        update `admin`
        <set>
            <if test="username != null">
                username = #{username},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="avatar != null">
                avatar = #{avatar},
            </if>
            <if test="role != null">
                role = #{role},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>