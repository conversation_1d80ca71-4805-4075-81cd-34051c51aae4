package com.example.mapper;

import com.example.entity.Province;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ProvinceMapper {

    int insert(Province province);

    void updateById(Province province);

    void deleteById(Integer id);

    @Select("select * from `province` where id = #{id}")
    Province selectById(Integer id);

    List<Province> selectAll(Province province);

}
