<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.BusinessOrdersMapper">

    <select id="selectAll" resultType="com.example.entity.BusinessOrders">
        select business_orders.*,user.name as userName,user.avatar as userAvatar,type.name as typeName,business.name as businessName from `business_orders`
        left join user on business_orders.user_id =user.id
        left join type on business_orders.type_id = type.id
        left join business on business_orders.business_id = business.id
        <where>
            <if test="orderNo != null"> and order_no = #{orderNo}</if>
            <if test="status != null"> and business_orders.status = #{status}</if>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="businessId != null"> and business_orders.business_id = #{businessId}</if>
            <if test="typeId != null"> and business_orders.type_id = #{typeId}</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `business_orders`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.BusinessOrders" useGeneratedKeys="true">
        insert into `business_orders`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="typeId != null">type_id,</if>
            <if test="businessId != null">business_id,</if>
            <if test="start != null">start,</if>
            <if test="end != null">end,</if>
            <if test="price != null">price,</if>
            <if test="payNo != null">pay_no,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="status != null">status,</if>
            <if test="score != null">score,</if>
            <if test="comment != null">comment,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="start != null">#{start},</if>
            <if test="end != null">#{end},</if>
            <if test="price != null">#{price},</if>
            <if test="payNo != null">#{payNo},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="status != null">#{status},</if>
            <if test="score != null">#{score},</if>
            <if test="comment != null">#{comment},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.BusinessOrders">
        update `business_orders`
        <set>
            <if test="payNo != null">
                pay_no = #{payNo},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="score != null">
                score = #{score},
            </if>
            <if test="comment != null">
                comment = #{comment},
            </if>
            <if test="commentTime != null">
                comment_time = #{commentTime},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>