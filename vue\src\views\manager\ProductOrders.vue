<template>
  <div>
    <div class="card" style="margin-bottom: 5px">
      <el-input v-model="data.orderNo" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输入订单编号查询"></el-input>
      <el-select v-model="data.status" placeholder="请选择订单状态查询" style="width:240px;margin-right:10px;">
        <el-option label="待支付" value="待支付"/>
        <el-option label="待发货" value="待发货"/>
        <el-option label="待签收" value="待签收"/>
        <el-option label="已签收" value="已签收"/>
        <el-option label="已取消" value="已取消"/>
      </el-select>
      <el-button type="info" plain @click="load">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div class="card" style="margin-bottom: 5px">
      <el-table stripe :data="data.tableData">
        <el-table-column prop="orderNo" label="订单号" show-overflow-tooltip />
        <el-table-column prop="userName" label="下单用户" width="120"  />
        <el-table-column prop="name" label="下单人" />
        <el-table-column prop="phone" label="联系电话" show-overflow-tooltip />
        <el-table-column prop="address" label="地址" show-overflow-tooltip />
        <el-table-column prop="productName" label="商品" width="120" />
        <el-table-column prop="num" label="数量" />
        <el-table-column prop="price" label="总金额" >
          <template v-slot="scope">
            <span style="color:red">￥{{scope.row.price}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="time" label="下单时间" show-overflow-tooltip />
        <el-table-column prop="payTime" label="支付时间" show-overflow-tooltip />
        <el-table-column prop="payNo" label="支付编号" show-overflow-tooltip />
        <el-table-column prop="status" label="订单状态" >
          <template v-slot="scope">
            <el-tag v-if="scope.row.status === '待支付'" type="warning">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '待发货'" type="info">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '待签收'" type="primary">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '已签收'" type="success">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '已取消'" type="danger">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template v-slot="scope">
            <el-button v-if="scope.row.status === '待支付'" type="danger" @click="cancel(scope.row)">取消</el-button>
            <el-button v-if="scope.row.status === '待发货'" type="info" @click="changeStatus(scope.row,'待签收')">发货</el-button>
          </template>
        </el-table-column>
      </el-table>

    </div>
    <div class="card" v-if="data.total">
      <el-pagination @current-change="load" background layout="prev, pager, next" :page-size="data.pageSize" v-model:current-page="data.pageNum" :total="data.total" />
    </div>
  </div>
</template>

<script setup>

import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";


const data = reactive({
  form: {},
  tableData: [],
  pageNum: 1,
  pageSize: 6,
  total: 0,
  orderNo: null,
  status:null,
})

const load = () => {
  request.get('/productOrders/selectPage', {
    params: {
      pageNum: data.pageNum,
      pageSize: data.pageSize,
      orderNo: data.orderNo,
      status: data.status
    }
  }).then(res => {
    if (res.code === '200') {
      data.tableData = res.data?.list || []
      data.total = res.data?.total
    }
  })
}


const update = () => {
  request.put('/productOrders/update', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    }
  })
}
const cancel = (row) =>{
  ElMessageBox.confirm('取消后订单无法恢复，您确定取消吗？', '取消订单确认', { type: 'warning' }).then(res => {
    request.put('/productOrders/cancel',row).then(res =>{
      if(res.code === '200'){
        ElMessage.success('操作成功')
        load()
      }else{
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const changeStatus = (row,status) => {
  data.form = JSON.parse(JSON.stringify(row))
  data.form.status = status
  update()
}
const reset = () => {
  data.orderNo = null
  data.status = null
  load()
}
load()
</script>