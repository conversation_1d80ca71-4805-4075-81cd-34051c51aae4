import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    { path: '/', redirect: '/login' },
    {
      path: '/manager',
      name: 'Manager',
      component: () => import('@/views/Manager.vue'),
      redirect: '/manager/home',
      children: [
        { path: 'home', meta: { name: '系统首页' }, component: () => import('@/views/manager/Home.vue'),  },
        { path: 'home2', meta: { name: '数据统计' }, component: () => import('@/views/manager/Home2.vue'),  },
        { path: 'homeBusiness', meta: { name: '数据统计' }, component: () => import('@/views/manager/HomeBusiness.vue'),  },
        { path: 'admin', meta: { name: '管理员信息' }, component: () => import('@/views/manager/Admin.vue'), },
        { path: 'notice', meta: { name: '系统公告' }, component: () => import('@/views/manager/Notice.vue'), },
        { path: 'person', meta: { name: '个人资料' }, component: () => import('@/views/manager/Person.vue'), },
        { path: 'password', meta: { name: '修改密码' }, component: () => import('@/views/manager/Password.vue'), },
        { path: 'user', meta: { name: '用户信息' }, component: () => import('@/views/manager/User.vue'), },
        { path: 'business', meta: { name: '商家信息' }, component: () => import('@/views/manager/Business.vue'), },
        { path: 'province', meta: { name: '所在省份' }, component: () => import('@/views/manager/Province.vue'), },
        { path: 'personBusiness', meta: { name: '宾馆资料' }, component: () => import('@/views/manager/PersonBusiness.vue'), },
        { path: 'authentication', meta: { name: '资格认证' }, component: () => import('@/views/manager/Authentication.vue'), },
        { path: 'feedback', meta: { name: '反馈信息' }, component: () => import('@/views/manager/Feedback.vue'), },
        { path: 'type', meta: { name: '房间类型' }, component: () => import('@/views/manager/Type.vue'), },
        { path: 'room', meta: { name: '房间信息' }, component: () => import('@/views/manager/Room.vue'), },
        { path: 'scenery', meta: { name: '景点' }, component: () => import('@/views/manager/Scenery.vue'), },
        { path: 'article', meta: { name: '旅游帖子' }, component: () => import('@/views/manager/Article.vue'), },
        { path: 'product', meta: { name: '省份特产' }, component: () => import('@/views/manager/Product.vue'), },
        { path: 'productOrders', meta: { name: '特产订单' }, component: () => import('@/views/manager/ProductOrders.vue'), },
        { path: 'comment', meta: { name: '帖子评论' }, component: () => import('@/views/manager/Comment.vue'), },
        { path: 'orders', meta: { name: '房间订单' }, component: () => import('@/views/manager/Orders.vue'), },
        { path: 'registration', meta: { name: '入住订单' }, component: () => import('@/views/manager/Registration.vue'), },
      ]
    },
    {
      path: '/front',
      component: () => import('@/views/Front.vue'),
      children: [
        { path: 'home', component: () => import('@/views/front/Home.vue'),  },
        { path: 'person', component: () => import('@/views/front/Person.vue'),  },
        { path: 'password', component: () => import('@/views/front/Password.vue'),  },
        { path: 'feedback', component: () => import('@/views/front/Feedback.vue'),  },
        { path: 'myFeedback', component: () => import('@/views/front/MyFeedback.vue'),  },
        { path: 'notice', component: () => import('@/views/front/Notice.vue'),  },
        { path: 'scenery', component: () => import('@/views/front/Scenery.vue'),  },
        { path: 'sceneryDetail', component: () => import('@/views/front/SceneryDetail.vue'),  },
        { path: 'article', component: () => import('@/views/front/Article.vue'),  },
        { path: 'myArticle', component: () => import('@/views/front/MyArticle.vue'),  },
        { path: 'articleDetail', component: () => import('@/views/front/ArticleDetail.vue'),  },
        { path: 'product', component: () => import('@/views/front/Product.vue'),  },
        { path: 'productOrders', component: () => import('@/views/front/ProductOrders.vue'),  },
        { path: 'businessOrders', component: () => import('@/views/front/BusinessOrders.vue'),  },
        { path: 'businessDetail', component: () => import('@/views/front/BusinessDetail.vue'),  },
        { path: 'typeDetail', component: () => import('@/views/front/TypeDetail.vue'),  },
        { path: 'business', component: () => import('@/views/front/Business.vue'),  },
        { path: 'myCollect', component: () => import('@/views/front/MyCollect.vue'),  },
      ]
    },
    { path: '/login', component: () => import('@/views/Login.vue') },
    { path: '/register', component: () => import('@/views/Register.vue') },
    { path: '/404', component: () => import('@/views/404.vue') },
    { path: '/:pathMatch(.*)', redirect: '/404' }
  ]
})

export default router
