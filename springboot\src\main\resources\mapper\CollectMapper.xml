<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.CollectMapper">

    <select id="selectAll" resultType="com.example.entity.Collect">
        select * from `collect`
        <where>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="typeId != null"> and type_id = #{typeId}</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `collect`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Collect" useGeneratedKeys="true">
        insert into `collect`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="typeId != null">type_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="typeId != null">#{typeId},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Collect">
        update `collect`
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>