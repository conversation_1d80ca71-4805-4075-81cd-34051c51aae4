<template>
  <div>
    <div class="card" style="margin-bottom: 5px">
      <el-input v-model="data.orderNo" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输入订单编号查询"></el-input>
      <el-select v-model="data.status" placeholder="请选择订单状态查询" style="width:240px;margin-right:10px;">
        <el-option label="待支付" value="待支付"/>
        <el-option label="待入住" value="待入住"/>
        <el-option label="已入住" value="已入住"/>
        <el-option label="已取消" value="已取消"/>
        <el-option label="已退房" value="已退房"/>
      </el-select>
      <el-button type="info" plain @click="load">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div class="card" style="margin-bottom: 5px">
      <el-table stripe :data="data.orderData">
        <el-table-column prop="orderNo" label="订单号" show-overflow-tooltip />
        <el-table-column prop="userName" label="下单用户" width="120"  />
        <el-table-column prop="businessName" label="宾馆名称" width="120" />
        <el-table-column prop="typeName" label="房间类型" width="120" />
        <el-table-column prop="start" label="入住时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="end" label="离开时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="price" label="总金额" >
          <template v-slot="scope">
            <span style="color:red">￥{{scope.row.price}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="payTime" label="支付时间" show-overflow-tooltip />
        <el-table-column prop="payNo" label="支付编号" show-overflow-tooltip />
        <el-table-column prop="status" label="订单状态" >
          <template v-slot="scope">
            <el-tag v-if="scope.row.status === '待支付'" type="warning">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '待入住'" type="info">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '已取消'" type="danger">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '已退房'" type="primary">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '已入住'" type="success">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template v-slot="scope">
            <el-button  v-if="scope.row.status === '待入住'" type="info"  @click="registrationInit(scope.row)">入住登记</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="card" v-if="data.total">
      <el-pagination @current-change="load" background layout="prev, pager, next" :page-size="data.pageSize" v-model:current-page="data.pageNum" :total="data.total" />
    </div>
    <el-dialog title="入住登记" v-model="data.formVisible" width="40%" destroy-on-close>
      <el-form ref="formRef" :rules="data.rules" :model="data.form" label-width="90px" style="padding: 20px">
        <el-form-item prop="roomId" label="可选房间">
          <el-select v-model="data.form.roomId" placeholder="请选择房间类型" style="width: 100%">
            <el-option
                v-for="item in data.roomData"
                :key="item.id"
                :label="item.name + '-' + item.typeName  "
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="inTime" label="入住日期">
          <el-date-picker
              v-model="data.form.inTime"
              type="date"
              placeholder="请选择入住日期"
              value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="registration">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    
  </div>
</template>

<script setup>

import {reactive,ref} from "vue";
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";

const formRef = ref()
const data = reactive({
  orderNo:null,
  status:null,
  total:0,
  pageNum:1,
  pageSize:5,
  orderData:[],
  form:{},
  formVisible:false,
  roomData:[],
  rules: {
    roomId: [
      { required: true, message: '请选择房间', trigger: 'blur' }
    ],
    inTime: [
      { required: true, message: '请选择登记日期', trigger: 'blur' }
    ],
  }
})
const registrationInit = (row) =>{
  data.form ={}
  data.form.orderNo = row.orderNo
  data.form.userId = row.userId
  data.form.businessId = row.businessId
  data.form.typeId = row.typeId
  data.form.orderId = row.id
  request.get('/room/selectAll',{
    params :{
      typeId:row.typeId,
      status:'空闲'
    }
  }).then(res =>{
    if(res.code === '200'){
      data.roomData = res.data
      data.formVisible = true
    }else{
      ElMessage.error(res.msg)
    }
  })


}
const registration = () =>{
   formRef.value.validate(valid =>{
     if(valid){
       request.post('/registration/add',data.form).then(res =>{
         if(res.code === '200'){
           ElMessage.success('入住登记成功')
           data.formVisible = false
           load()
         }else{
           ElMessage.error(res.msg)
         }
       })
     }
   })
}

const load = () => {
  request.get('/businessOrders/selectPage', {
    params: {
      pageNum: data.pageNum,
      pageSize: data.pageSize,
      orderNo:data.orderNo,
      status:data.status,
    }
  }).then(res => {
    if (res.code === '200') {
      data.orderData = res.data?.list || []
      data.total = res.data?.total
    }
  })
}


const update = () => {
  request.put('/businessOrders/update', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    }
  })
}

const del = (id) => {
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete('/businessOrders/delete/' + id).then(res => {
      if (res.code === '200') {
        ElMessage.success("删除成功")
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const delBatch = () => {
  if (!data.ids.length) {
    ElMessage.warning("请选择数据")
    return
  }
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete("/businessOrders/delete/batch", {data: data.ids}).then(res => {
      if (res.code === '200') {
        ElMessage.success('操作成功')
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}

const reset = () => {
  data.orderNo = null
  data.status = null
  load()
}

load()
</script>