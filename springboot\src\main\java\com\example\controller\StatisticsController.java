package com.example.controller;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.example.common.Result;
import com.example.entity.*;
import com.example.mapper.BusinessMapper;
import com.example.service.*;
import com.example.utils.TokenUtils;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/statistics")
public class StatisticsController {
    @Resource
    private BusinessService businessService;
    @Resource
    private ProductService productService;
    @Resource
    private SceneryService sceneryService;
    @Resource
    private ProductOrdersService productOrdersService;
    @Resource
    private BusinessOrdersService businessOrdersService;
    @Resource
    private ArticleService articleService;
    @Resource
    private TypeService typeService;
    @Resource
    private RoomService roomService;
    @Resource
    private ProvinceService provinceService;

   @GetMapping("/base")
    public Result base(){
       Map<String,Object> map = new HashMap<>();
       Double businessTotal = businessOrdersService.selectAll(new BusinessOrders())
               .stream()
               .filter(x -> !"待支付".equals(x.getStatus()))
               .map(BusinessOrders::getPrice)
               .reduce(0.0,Double::sum);
       Double productTotal = productOrdersService.selectAll(new ProductOrders())
               .stream()
               .filter(x -> !"待支付".equals(x.getStatus()))
               .map(ProductOrders::getPrice)
               .reduce(0.0,Double::sum);
       map.put("businessTotal",businessTotal);
       map.put("productTotal",productTotal);
       map.put("businessNum",businessService.selectAll(new Business()).size());
       map.put("productNum",productService.selectAll(new Product()).size());
       map.put("sceneryNum",sceneryService.selectAll(new Scenery()).size());
       map.put("articleNum",articleService.selectAll(new Article()).size());
        return Result.success(map);
    }
    @GetMapping("/baseBusiness")
    public Result baseBusiness(){
       Account currentUser = TokenUtils.getCurrentUser();
        Map<String,Object> map = new HashMap<>();
        Double businessTotal = businessOrdersService.selectAll(new BusinessOrders())
                .stream()
                .filter(x -> !"待支付".equals(x.getStatus()) && currentUser.getId().equals(x.getBusinessId()))
                .map(BusinessOrders::getPrice)
                .reduce(0.0,Double::sum);
        long typeNum = typeService.selectAll(new Type()).stream().filter(x -> x.getBusinessId().equals(currentUser.getId())).count();
        long emptyNum =roomService.selectAll(new Room())
                .stream()
                .filter(x -> x.getBusinessId()
                        .equals(currentUser.getId()) && "空闲".equals(x.getStatus())).count();
        long usingNum =roomService.selectAll(new Room())
                .stream()
                .filter(x -> x.getBusinessId()
                        .equals(currentUser.getId()) && "使用中".equals(x.getStatus())).count();
        map.put("businessTotal",businessTotal);
        map.put("typeNum",typeNum);
        map.put("emptyNum",emptyNum);
        map.put("usingNum",usingNum);
        return Result.success(map);
    }

    @GetMapping("/line")
    public Result line(){
       Map<String,Object> map = new HashMap<>();
        Date today = new Date();
        DateTime start = DateUtil.offsetDay(today, -7);
        List<String> xList = DateUtil.rangeToList(start, today, DateField.DAY_OF_YEAR).stream().map(DateUtil::formatDate).toList();
        List<BusinessOrders> orders =businessOrdersService.selectAll(new BusinessOrders())
                .stream().filter(x -> !"待支付".equals(x.getStatus()) && !"已取消".equals(x.getStatus())).collect(Collectors.toList());
        List<Double> yList = new ArrayList<>();
        for(String day: xList){
            Double total = orders.stream().filter(x ->x.getPayTime().contains(day)).map(BusinessOrders::getPrice).reduce(0.0,Double::sum);
            yList.add(total);
        }
       map.put("xList",xList);
       map.put("yList",yList);
       return Result.success(map);
   }
    @GetMapping("/lineBusiness")
    public Result lineBusiness(){
       Account currentUser = TokenUtils.getCurrentUser();
        Map<String,Object> map = new HashMap<>();
        Date today = new Date();
        DateTime start = DateUtil.offsetDay(today, -7);
        List<String> xList = DateUtil.rangeToList(start, today, DateField.DAY_OF_YEAR).stream().map(DateUtil::formatDate).toList();
        List<BusinessOrders> orders =businessOrdersService.selectAll(new BusinessOrders())
                .stream().filter(x -> !"待支付".equals(x.getStatus()) && !"已取消".equals(x.getStatus()) && x.getBusinessId().equals(currentUser.getId())).collect(Collectors.toList());
        List<Double> yList = new ArrayList<>();
        for(String day: xList){
            Double total = orders.stream().filter(x ->x.getPayTime().contains(day)).map(BusinessOrders::getPrice).reduce(0.0,Double::sum);
            yList.add(total);
        }
        map.put("xList",xList);
        map.put("yList",yList);
        return Result.success(map);
    }
 //同一宾馆房间类型数量图
    @GetMapping("/pieBusiness")
    public Result pieBusiness(){
     Account currentUser = TokenUtils.getCurrentUser();
       List<Map<String ,Object>> list = new ArrayList<>();
       List<Type> types =typeService.selectAll(new Type()).stream().filter(x -> x.getBusinessId().equals(currentUser.getId())).collect(Collectors.toList());
       List<Room> rooms =roomService.selectAll(new Room()).stream().filter(x -> x.getBusinessId().equals(currentUser.getId())).collect(Collectors.toList());
       for(Type type :types){
           Map<String,Object> map = new HashMap<>();
           map.put("name",type.getName());
           map.put("value",rooms.stream().filter(x ->x.getTypeId().equals(type.getId())).count());
           list.add(map);
       }
       return Result.success(list);
    }
    //不同省份宾馆数量图
    @GetMapping("/pie")
    public Result pie(){
        List<Map<String ,Object>> list = new ArrayList<>();
        List<Province> provinces = provinceService.selectAll(new Province());
        List<Business> businesses = businessService.selectAll(new Business());
        for(Province province : provinces){
            Map<String,Object> map =new HashMap<>();
            map.put("name",province.getName());
            map.put("value",businesses.stream().filter(x ->x.getProvinceId().equals(province.getId())).count());
            list.add(map);
        }
        return Result.success(list);
    }
//同一商家空闲房间数量图
    @GetMapping("barBusiness")
    public Result barBusiness(){
       Account currentUser = TokenUtils.getCurrentUser();
        Map<String,Object> map = new HashMap<>();
        List<Type> types =typeService.selectAll(new Type()).stream().filter(x -> x.getBusinessId().equals(currentUser.getId())).collect(Collectors.toList());
        List<Room> rooms =roomService.selectAll(new Room()).stream().filter(x -> x.getBusinessId().equals(currentUser.getId())).collect(Collectors.toList());
        List<String> xList = new ArrayList<>();
        List<Object> yList = new ArrayList<>();
        for(Type type:types){
            xList.add(type.getName());
            yList.add(rooms.stream().filter(x ->x.getTypeId().equals(type.getId()) && "空闲".equals(x.getStatus())).count());
        }
        map.put("xList",xList);
        map.put("yList",yList);
        return Result.success(map);
    }
    @GetMapping("bar")
    public Result bar(){
        Map<String,Object> map = new HashMap<>();
        List<String> xList = new ArrayList<>();
        List<Object> yList = new ArrayList<>();
        List<Business> businesses =  businessService.selectAll(new Business());
        List<BusinessOrders> orders = businessOrdersService.selectAll(new BusinessOrders())
                .stream()
                .filter(x -> !"待支付".equals(x.getStatus())).collect(Collectors.toList());
        for(Business business : businesses){
            xList.add(business.getName());
            yList.add(orders.stream().filter(x ->x.getBusinessId().equals(business.getId())).map(BusinessOrders::getPrice).reduce(0.0,Double::sum));
        }
        map.put("xList",xList);
        map.put("yList",yList);
        return Result.success(map);
    }
}
