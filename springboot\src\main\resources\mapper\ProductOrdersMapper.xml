<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.ProductOrdersMapper">

    <select id="selectAll" resultType="com.example.entity.ProductOrders">
        select product_orders.*,user.name as userName,product.name as productName from `product_orders`
        left join user on product_orders.user_id =user.id
        left join product on product_orders.product_id = product.id
        <where>
            <if test="orderNo != null"> and order_no = #{orderNo}</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="userId != null"> and user_id = #{userId}</if>
        </where>
        order by id desc
    </select>

    <delete id="deleteById">
        delete from `product_orders`
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.ProductOrders" useGeneratedKeys="true">
        insert into `product_orders`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="num != null">num,</if>
            <if test="price != null">price,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="address != null">address,</if>
            <if test="time != null">time,</if>
            <if test="payNo != null">pay_no,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="num != null">#{num},</if>
            <if test="price != null">#{price},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="address != null">#{address},</if>
            <if test="time != null">#{time},</if>
            <if test="payNo != null">#{payNo},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.ProductOrders">
        update `product_orders`
        <set>
            <if test="payNo != null">
                pay_no = #{payNo},
            </if>
            <if test="payTime != null">
                pay_time = #{payTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>