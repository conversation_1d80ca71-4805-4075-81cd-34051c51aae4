<template>
  <div  style="width:80%;margin:20px auto;min-height:600px">
   <div style="font-size:18px">特产订单（{{ data.total }}）</div>
    <div class="card" style="margin:20px 0">
      <el-input v-model="data.orderNo" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输入订单编号查询"></el-input>
      <el-select v-model="data.status" placeholder="请选择订单状态查询" style="width:240px;margin-right:10px;">
        <el-option label="待支付" value="待支付"/>
        <el-option label="待发货" value="待发货"/>
        <el-option label="待签收" value="待签收"/>
        <el-option label="已签收" value="已签收"/>
        <el-option label="已取消" value="已取消"/>
      </el-select>
      <el-button type="info" plain @click="loadOrders">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div class="card" style="margin-bottom: 5px">
      <el-table stripe :data="data.ordersData">
        <el-table-column prop="orderNo" label="订单号" show-overflow-tooltip />
        <el-table-column prop="userName" label="下单用户" width="120"  />
        <el-table-column prop="name" label="下单人" />
        <el-table-column prop="phone" label="联系电话" show-overflow-tooltip />
        <el-table-column prop="address" label="地址" show-overflow-tooltip />
        <el-table-column prop="productName" label="商品" width="120" />
        <el-table-column prop="num" label="数量" />
        <el-table-column prop="price" label="总金额" >
          <template v-slot="scope">
            <span style="color:red">￥{{scope.row.price}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="time" label="下单时间" show-overflow-tooltip />
        <el-table-column prop="payTime" label="支付时间" show-overflow-tooltip />
        <el-table-column prop="payNo" label="支付编号" show-overflow-tooltip />
        <el-table-column prop="status" label="订单状态" >
          <template v-slot="scope">
            <el-tag v-if="scope.row.status === '待支付'" type="warning">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '待发货'" type="info">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '待签收'" type="primary">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '已签收'" type="success">{{ scope.row.status }}</el-tag>
            <el-tag v-if="scope.row.status === '已取消'" type="danger">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template v-slot="scope">
            <el-button size="small" v-if="scope.row.status === '待支付'" type="info"  @click="payInit(scope.row)">支付</el-button>
            <el-button size="small" v-if="scope.row.status === '待签收'" type="primary" @click="changeStatus(scope.row,'已签收')">签收</el-button>
            <el-button size="small" v-if="scope.row.status === '待支付'" type="danger" @click="cancel(scope.row)">取消</el-button>
            <el-button size="small" v-if="scope.row.status === '已取消'||scope.row.status === '已签收'" type="danger" @click="del(scope.row.id)">删除订单</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="card" v-if="data.total">
      <el-pagination @current-change="loadOrders" background layout="prev, pager, next" :page-size="data.pageSize" v-model:current-page="data.pageNum" :total="data.total" />
    </div>
    <el-dialog title="支付订单" v-model="data.formVisible" width="40%" destroy-on-close>
      <el-form ref="form" :model="data.form"  label-width="70px" style="padding: 20px">
        <el-form-item prop="type" label="支付方式">
          <el-radio-group v-model="data.type" fill="#A3A6AD" >
            <el-radio-button label="支付宝" value="aliPay" />
            <el-radio-button label="微信" value="wePay" />
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="price" label="支付金额">
          <span style="color:red">￥{{data.form.price}}</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="pay">支 付</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";
const data = reactive({
  orderNo:null,
  status:null,
  total:0,
  pageNum:1,
  pageSize:5,
  ordersData:[],
  form:{},
  type:'aliPay',
  formVisible:false,
})
const payInit = (row) =>{
  data.form = JSON.parse(JSON.stringify(row))
  data.formVisible = true
}
const pay = () =>{
  request.put('/productOrders/pay',data.form).then(res =>{
    if(res.code === '200'){
      ElMessage.success('下单成功')
      data.formVisible = false
      loadOrders()
    }else{
      ElMessage.error(res.msg)
    }
  })
}
const cancel = (row) =>{
  ElMessageBox.confirm('取消后订单无法恢复，您确定取消吗？', '取消订单确认', { type: 'warning' }).then(res => {
    request.put('/productOrders/cancel',row).then(res =>{
      if(res.code === '200'){
        ElMessage.success('操作成功')
        loadOrders()
      }else{
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const del = (id) =>{
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete('/productOrders/delete/' + id).then(res =>{
      if(res.code === '200'){
        ElMessage.success('删除订单成功')
        loadOrders()
      }else{
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const loadOrders = () =>{
  request.get('/productOrders/selectPage', {
    params: {
      pageNum:data.pageNum,
      pageSize:data.pageSize,
      orderNo:data.orderNo,
      status:data.status,
    }
  }).then(res => {
    if(res.code === '200'){
      data.ordersData = res.data?.list
      data.total = res.data?.total
    }else{
      ElMessage.error(res.msg)
    }
  })
}
const update = () => {
  request.put('/productOrders/update', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      loadOrders()
    }
  })
}

const changeStatus = (row,status) => {
  data.form = JSON.parse(JSON.stringify(row))
  data.form.status = status
  update()
}
loadOrders()
const reset = () =>{
  data.orderNo = null
  data.status = null
  loadOrders()
}
</script>