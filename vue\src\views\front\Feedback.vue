<template>
 <div style="width:50% ;margin:20px auto;min-height:600px">
   <div class="card">
     <div style="color: lightskyblue;font-size:23px;text-align:center">提交您的反馈信息</div>
     <div>
       <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="70px" style="padding: 20px">
         <el-form-item prop="title" label="标题">
           <el-input v-model="data.form.title" placeholder="请输入反馈信息标题"></el-input>
         </el-form-item>
         <el-form-item prop="question" label="问题">
           <el-input type="textarea" :rows="4" v-model="data.form.question" placeholder="请输入反馈内容"></el-input>
         </el-form-item>
         <el-form-item prop="idea" label="想法">
           <el-input type="textarea" :rows="4" v-model="data.form.idea" placeholder="请输入您的想法"></el-input>
         </el-form-item>
       </el-form>
     </div>
     <div style="text-align: center">
       <el-button type="primary" @click="submit">提交</el-button>
     </div>
   </div>
 </div>
</template>
<script setup>
import {reactive,ref} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
const formRef = ref()
const data = reactive({
  form:{},
  rules:{
   title: [
      { required: true, message: '请输入标题', trigger: 'blur' },
    ],
    question: [
      { required: true, message: '请输入问题', trigger: 'blur' },
    ],
  }
})
const submit = () =>{
  formRef.value.validate(valid =>{
    if(valid){
      request.post('/feedback/add',data.form).then(res =>{
        if(res.code === '200'){
          ElMessage.success('提交成功，等待管理端进行回复')
          data.form={}
        }
        else{
          ElMessage.error(res.msg)
        }
      })
    }
  })

}
</script>