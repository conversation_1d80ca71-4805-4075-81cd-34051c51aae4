<template>
  <div>
    <div class="card" style="margin-bottom: 5px">
      <el-input v-model="data.name" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输入房间类型查询"></el-input>
      <el-button type="info" plain @click="load">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div class="card" style="margin-bottom: 5px" v-if="data.user.role === 'BUSINESS'">
      <el-button type="primary" plain @click="handleAdd">新增</el-button>
      <el-button type="danger" plain @click="delBatch">批量删除</el-button>
    </div>

    <div class="card" style="margin-bottom: 5px">
      <el-table stripe :data="data.tableData" @selection-change="handleSelectionChange" tooltip-effect="light myEffect">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="businessName" label="所属商家" />
        <el-table-column prop="name" label="房间名称" />
        <el-table-column prop="img" label="封面" >
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 5px; display: block" v-if="scope.row.img"
                      :src="scope.row.img" :preview-src-list="[scope.row.img]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="类型简介" show-overflow-tooltip />
        <el-table-column prop="price" label="房间价格" />
        <el-table-column prop="content" label="详细介绍" width="100" >
          <template v-slot="scope">
            <el-button type="primary" @click="view(scope.row.content)">点击查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="total" label="房间总数" />
        <el-table-column prop="num" label="空闲房间" />
        <el-table-column prop="img1" label="图片1">
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 5px; display: block" v-if="scope.row.img1"
                      :src="scope.row.img1" :preview-src-list="[scope.row.img1]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="img2" label="图片2" >
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 5px; display: block" v-if="scope.row.img2"
                      :src="scope.row.img2" :preview-src-list="[scope.row.img2]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template v-slot="scope">
            <el-button type="primary" circle :icon="Edit" @click="handleEdit(scope.row)" v-if="data.user.role === 'BUSINESS'"></el-button>
            <el-button type="danger" circle :icon="Delete" @click="del(scope.row.id)"></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="card" v-if="data.total">
      <el-pagination @current-change="load" background layout="prev, pager, next" :page-size="data.pageSize" v-model:current-page="data.pageNum" :total="data.total" />
    </div>

    <el-dialog title="房间类型信息" v-model="data.formVisible" width="50%" destroy-on-close>
      <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="110px" style="padding: 20px">
        <el-form-item prop="name" label="类型名称">
          <el-input v-model="data.form.name" placeholder="请输入类型名称"></el-input>
        </el-form-item>
        <el-form-item prop="description" label="房间类型简介" >
          <el-input type="textarea" :rows="4" v-model="data.form.description" placeholder="请输入类型名称"></el-input>
        </el-form-item>
        <el-form-item prop="price" label="房间价格">
          <el-input-number v-model="data.form.price" :min="100"></el-input-number>
        </el-form-item>
        <el-form-item prop="content" label="房间详细介绍">
          <div style="border: 1px solid #ccc; width: 100%">
            <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editor="editorRef"
                :mode="mode"
            />
            <Editor
                style="height: 500px; overflow-y: hidden;"
                v-model="data.form.content"
                :mode="mode"
                :defaultConfig="editorConfig"
                @onCreated="handleCreated"
            />
          </div>
        </el-form-item>
        <el-form-item prop="img" label="封面">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleImgUpload"
              list-type="picture"
          >
            <el-button type="primary">上传封面</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="img1" label="小图1">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleImg1Upload"
              list-type="picture"
          >
            <el-button type="primary">上传小图1</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="img2" label="小图2">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleImg2Upload"
              list-type="picture"
          >
            <el-button type="primary">上传小图2</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="save">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="详细介绍" v-model="data.viewVisible" width="50%" destroy-on-close>
      <div v-html="data.viewContent" style="padding: 20px"></div>
    </el-dialog>
  </div>
</template>

<script setup>

import {onBeforeUnmount, reactive, ref, shallowRef} from "vue";
import '@wangeditor/editor/dist/css/style.css';
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

const baseUrl = import.meta.env.VITE_BASE_URL
const formRef = ref()
const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
  formVisible: false,
  form: {},
  tableData: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  name: null,
  ids: [],
  viewContent:null,
  viewVisible:null,
  rules:{
    name: [
      { required: true, message: '请输入房间类型', trigger: 'blur' },
    ],
    description: [
      { required: true, message: '请输入房间类型简介', trigger: 'blur' },
    ],
  }
})
/* wangEditor5 初始化开始 */
const editorRef = shallowRef()  // 编辑器实例，必须用 shallowRef
const mode = 'default'
const editorConfig = { MENU_CONF: {} }
// 图片上传配置
editorConfig.MENU_CONF['uploadImage'] = {
  headers: {
    token: data.user.token,
  },
  server: baseUrl + '/files/wang/upload',  // 服务端图片上传接口
  fieldName: 'file'  // 服务端图片上传接口参数
}
// 组件销毁时，也及时销毁编辑器，否则可能会造成内存泄漏
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})
// 记录 editor 实例，重要！
const handleCreated = (editor) => {
  editorRef.value = editor
}
/* wangEditor5 初始化结束 */

const load = () => {
  request.get('/type/selectPage', {
    params: {
      pageNum: data.pageNum,
      pageSize: data.pageSize,
      name: data.name
    }
  }).then(res => {
    if (res.code === '200') {
      data.tableData = res.data?.list || []
      data.total = res.data?.total
    }
  })
}
const handleAdd = () => {
  data.form = {}
  data.formVisible = true
}
const handleEdit = (row) => {
  data.form = JSON.parse(JSON.stringify(row))
  data.formVisible = true
}
const add = () => {
  request.post('/type/add', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const view = (content) => {
  data.viewContent = content
  data.viewVisible = true
}
const update = () => {
  request.put('/type/update', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    }
  })
}

const save = () => {
  data.form.id ? update() : add()
}

const del = (id) => {
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete('/type/delete/' + id).then(res => {
      if (res.code === '200') {
        ElMessage.success("删除成功")
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const delBatch = () => {
  if (!data.ids.length) {
    ElMessage.warning("请选择数据")
    return
  }
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete("/type/delete/batch", {data: data.ids}).then(res => {
      if (res.code === '200') {
        ElMessage.success('操作成功')
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const handleSelectionChange = (rows) => {
  data.ids = rows.map(v => v.id)
}

const reset = () => {
  data.name = null
  load()
}
const handleImgUpload = (res) =>{
  data.form.img =res.data
}
const handleImg2Upload = (res) =>{
  data.form.img2 =res.data
}

const handleImg1Upload = (res) =>{
  data.form.img1 =res.data
}

load()
</script>
<style>
.myEffect{
  width:40%
}
</style>