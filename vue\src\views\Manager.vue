<template>
  <div class="manager-container">
    <div class="manager-header">
      <div class="manager-header-left">
        <img src="@/assets/imgs/logo.png" alt="">
        <div class="title">小型宾馆管理系统</div>
      </div>
      <div class="manager-header-center">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/manager/home' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>{{ router.currentRoute.value.meta.name }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="manager-header-right">
        <el-dropdown style="cursor: pointer">
          <div style="padding-right: 20px; display: flex; align-items: center">
            <img style="width: 40px; height: 40px; border-radius: 50%;" :src="data.user.avatar" alt="">
            <span style="margin-left: 5px; color: white">{{ data.user.name }}</span><el-icon color="#fff"><arrow-down /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-if="data.user.role === 'ADMIN'" @click="router.push('/manager/person')">管理员资料</el-dropdown-item>
              <el-dropdown-item v-if="data.user.role === 'BUSINESS'" @click="router.push('/manager/personBusiness')">宾馆资料</el-dropdown-item>
              <el-dropdown-item v-if="data.user.role === 'BUSINESS'" @click="router.push('/manager/authentication')">资格认证</el-dropdown-item>
              <el-dropdown-item @click="router.push('/manager/password')">修改密码</el-dropdown-item>
              <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <!-- 下面部分开始 -->
    <div style="display: flex">
      <div class="manager-main-left">
        <el-menu :default-active="router.currentRoute.value.path"
                 :default-openeds="['1', '2']"
                 router
        >
          <el-menu-item index="/manager/home">
            <el-icon><HomeFilled /></el-icon>
            <span>系统首页</span>
          </el-menu-item>
          <el-menu-item index="/manager/home2" v-if=" data.user.role ==='ADMIN'">
            <el-icon><PieChart /></el-icon>
            <span>数据统计</span>
          </el-menu-item>
          <el-menu-item index="/manager/homeBusiness" v-if=" data.user.role ==='BUSINESS'">
            <el-icon><PieChart /></el-icon>
            <span>数据统计</span>
          </el-menu-item>
          <el-sub-menu index="1" v-if=" data.user.role === 'ADMIN'|| (data.user.role === 'BUSINESS' && data.user.status ==='通过')">
            <template #title>
              <el-icon><TurnOff /></el-icon>
              <span>信息管理</span>
            </template>
            <el-menu-item index="/manager/notice" v-if="data.user.role === 'ADMIN'">系统公告</el-menu-item>
            <el-menu-item index="/manager/province" v-if="data.user.role === 'ADMIN'">所在省份</el-menu-item>
            <el-menu-item index="/manager/type">房间类型</el-menu-item>
            <el-menu-item index="/manager/room">房间信息</el-menu-item>
            <el-menu-item index="/manager/orders" v-if="data.user.role ==='BUSINESS'">房间订单</el-menu-item>
            <el-menu-item index="/manager/registration" v-if="data.user.role ==='BUSINESS'">入住登记</el-menu-item>
            <el-menu-item index="/manager/scenery">热门景点</el-menu-item>
            <el-menu-item index="/manager/article" >旅游帖子</el-menu-item>
            <el-menu-item index="/manager/comment" >帖子评论</el-menu-item>
            <el-menu-item index="/manager/product" >当地特产</el-menu-item>
            <el-menu-item index="/manager/productOrders" >特产订单</el-menu-item>
            <el-menu-item index="/manager/feedback" v-if="data.user.role === 'ADMIN'">反馈建议</el-menu-item>
          </el-sub-menu>
          <el-sub-menu index="2" v-if="data.user.role === 'ADMIN'">
            <template #title>
              <el-icon><Menu /></el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="/manager/admin">管理员信息</el-menu-item>
            <el-menu-item index="/manager/user">用户信息</el-menu-item>
            <el-menu-item index="/manager/business">商家信息</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div>
      <div class="manager-main-right">
        <RouterView @updateUser="updateUser" />
      </div>
    </div>
    <!-- 下面部分结束 -->


  </div>
</template>

<script setup>
import { reactive } from "vue";
import router from "@/router/index.js";
import {ElMessage} from "element-plus";

const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}')
})

const logout = () => {
  localStorage.removeItem('xm-user')
  router.push('/login')
}

const updateUser = () => {
  data.user =  JSON.parse(localStorage.getItem('xm-user') || '{}')
}

if (!data.user.id) {
  logout()
  ElMessage.error('请登录！')
}
</script>

<style scoped>
@import "@/assets/css/manager.css";
</style>