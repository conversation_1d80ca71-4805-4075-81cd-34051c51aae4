<template>
  <div  style="width:80%;margin:20px auto;min-height:600px">
    <div style="color:darkorchid;font-size:18px; margin-top:20px">我的收藏 （{{data.collectData.length}}）</div>
    <div style="margin-top: 20px">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in data.collectData" style="margin-bottom: 20px">
          <div class="front_card">
            <img @click="navTo('/front/typeDetail?id=' + item.typeId)" :src="item.typeImg" alt="" style="width: 100%; height: 200px; border-radius: 5px 5px 0 0; cursor: pointer">
            <div style="padding: 10px">
              <div style="display: flex; align-items: center">
                <el-icon size="20"><OfficeBuilding /></el-icon>
                <div style="font-weight: bold; font-size: 16px; margin-left: 5px" class="line1">{{ item.businessName }}</div>
              </div>
              <div style="display: flex; align-items: center; margin-top: 10px">
                <el-icon><LocationInformation /></el-icon>
                <div style="color: #8a8a8a; margin-left: 5px" class="line1">房间类型：{{ item.typeName }}</div>
              </div>
              <div style="display: flex; align-items: center; margin-top: 10px">
                <el-icon><Goods /></el-icon>
                <div style="margin-left: 5px"><span style="font-weight: bold; font-size: 18px; color: red">￥{{ item.typePrice }}</span> / 晚</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script setup>
import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
const data = reactive({
  user: JSON.parse(localStorage.getItem('xm-user') || '{}'),
  collectData:[],
})
const loadCollect = () =>{
  request.get('/collect/selectAll',{
    params: {
      userId:data.user.id
    }
  }).then(res =>{
    if(res.code === '200'){
      data.collectData = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadCollect()
const navTo = (url) =>{
  location.href =url
}
</script>