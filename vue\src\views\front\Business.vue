<template>
  <div  style="width:80%;margin:20px auto;min-height:600px">
    <div>
      <el-input v-model="data.name" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输宾馆名称称查询">
      </el-input>
      <el-select v-model="data.provinceId" placeholder="请选择省份" style="width:240px;margin:0 10px">
        <el-option
            v-for="item in data.provinceData"
            :key="item.id"
            :label="item.name"
            :value="item.id"
        />
      </el-select>
      <el-button type="info" plain @click="loadBusiness">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div style="margin-top:20px">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in data.businessData" style="margin-bottom: 20px">
          <div class="front_card">
            <img @click="navTo('/front/businessDetail?id=' + item.id)" :src="item.img" alt="" style="width:100%; height:200px; border-radius:5px 5px 0 0;cursor:pointer">
            <div style="padding:10px">
              <div style="display:flex;align-items:center">
                <el-icon :size="20"><OfficeBuilding /></el-icon>
                <div style="font-size:16px;font-weight:bold;margin-left:5px">{{item.name}}</div>
              </div>
              <div style="display:flex;align-items:center;margin-top:10px">
                <el-icon><Location /></el-icon>
                <div style="margin-left:5px;color:#666" class="line1">{{item.address}}</div>
              </div>
              <div style="display:flex;align-items:center;margin-top:5px">
                <div>评分： </div>
                <el-rate v-model="item.score" disabled></el-rate>
              </div>
              <div style="margin-top:5px;color:red;">
                <span style="font-weight: bold; font-size:18px">￥{{item.price}}</span> 起
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script setup>
import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
const data = reactive({
 name:null,
  provinceId:null,
  provinceData:[],
  businessData:[],
})
const loadProvince = () =>{
  request.get('/province/selectAll').then(res =>{
    if(res.code === '200'){
      data.provinceData = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadProvince()
const loadBusiness = () =>{
  request.get('/business/selectAll',{
    params:{
       status:'通过',
      name:data.name,
      provinceId:data.provinceId
    }
  }).then(res =>{
    if(res.code ==='200'){
      data.businessData = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadBusiness()
const reset = () =>{
  data.name = null
  data.provinceId = null
  loadBusiness()
}
const navTo = (url) =>{
  location.href = url
}
</script>