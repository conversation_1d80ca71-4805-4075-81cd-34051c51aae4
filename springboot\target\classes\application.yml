server:
  port: 9090

# 数据库配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: '123456'
    url: ***********************************************************************************************************************************************************************
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# 配置mybatis实体和xml映射
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  mapper-locations: classpath:mapper/*.xml

fileBaseUrl: http://localhost:${server.port}
