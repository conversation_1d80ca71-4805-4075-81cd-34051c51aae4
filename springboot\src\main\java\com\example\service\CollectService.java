package com.example.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.example.common.enums.RoleEnum;
import com.example.entity.Account;
import com.example.entity.Business;
import com.example.entity.Collect;
import com.example.entity.Type;
import com.example.mapper.BusinessMapper;
import com.example.mapper.CollectMapper;
import com.example.mapper.TypeMapper;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层方法
 */
@Service
public class CollectService {

    @Resource
    private CollectMapper collectMapper;
    @Resource
    private TypeMapper typeMapper;
    @Resource
    private BusinessMapper businessMapper;

    public void add(Collect collect) {
        Account currentUser = TokenUtils.getCurrentUser();
        collect.setUserId(currentUser.getId());
        collectMapper.insert(collect);
    }

    public void updateById(Collect collect) {
        collectMapper.updateById(collect);
    }

    public void deleteById(Integer id) {
        collectMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            collectMapper.deleteById(id);
        }
    }

    public Collect selectById(Integer id) {
        return collectMapper.selectById(id);
    }

    public List<Collect> selectAll(Collect collect) {
        List<Collect> collects = collectMapper.selectAll(collect);
        for(Collect dbCollect :collects){
            Type type = typeMapper.selectById(dbCollect.getTypeId());
            if(ObjectUtil.isNotEmpty(type)){
                dbCollect.setTypeImg(type.getImg());
                dbCollect.setTypeName(type.getName());
                dbCollect.setTypePrice(type.getPrice());
                Business business = businessMapper.selectById(type.getBusinessId());
                if(ObjectUtil.isNotEmpty(business)){
                    dbCollect.setBusinessName(business.getName());
                }
            }
        }
        return collects;
    }

    public PageInfo<Collect> selectPage(Collect collect, Integer pageNum, Integer pageSize) {
        Account currentUser = TokenUtils.getCurrentUser();
        if(RoleEnum.USER.name().equals(currentUser.getRole())) {
            collect.setUserId(currentUser.getId());
        }
        PageHelper.startPage(pageNum, pageSize);
        List<Collect> list = collectMapper.selectAll(collect);
        return PageInfo.of(list);
    }


    public void deleteByUserId(Collect collect) {
        collectMapper.deleteByUserId(collect.getUserId(), collect.getTypeId());
    }
}
