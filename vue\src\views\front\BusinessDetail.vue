<template>
  <div  style="width:80%;margin:20px auto;min-height:600px">
   <div class="card" style="display:flex;grid-gap:20px">
     <img :src="data.businessData.img" alt="" style="width:500px;height:300px;border-radius:5px">
     <div style="flex:1;padding:15px">
       <div style="font-size:24px;font-weight:bold">{{data.businessData.name}}</div>
       <div style="font-size:15px;margin-top:13px">
         <div>宾馆网站：<a :href="data.businessData.link" target="_blank">{{data.businessData.link}}</a></div>
         <div style="margin-top:9px">联系电话：{{data.businessData.phone}}</div>
         <div style="margin-top:9px">官方邮箱：{{data.businessData.email}}</div>
         <div style="margin-top:9px">入住价格：<span style="font-weight:bold;font-size:20px;color:red;">￥{{data.businessData.price}}</span> 起</div>
         <div style="margin-top:9px">宾馆地址：{{data.businessData.address}}</div>
         <div style="margin-top:9px;line-height:25px;text-align:justify" class="line4">详细介绍：{{data.businessData.content}}</div>
       </div>
     </div>
   </div>
    <div style="margin-top:20px">
      <el-row :gutter="20">
        <el-col :span="6" v-for="item in data.typeData">
          <div class="front_card">
            <img @click="navTo('/front/typeDetail?id='+item.id)" :src="item.img" alt="" style="width:100%;height:200px;border-radius:5px 5px 0 0;cursor:pointer;">
            <div style="padding:20px">
              <div style="font-size:16px;font-weight:bold">{{item.name}}</div>
              <div style="display:flex;align-items:center;margin-top:15px">
                <el-icon size="18"><HomeFilled /></el-icon>
                <div style="flex:1;margin-right:5px">剩余 {{item.num}} 间</div>
                <div style="width:80px;font-weight: bold;font-size: 20px;color:red;text-align:right">￥{{item.price}}</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script setup>
import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
import router from "@/router/index.js";
const data = reactive({
  businessData:{},
  typeData:[],
  businessId:router.currentRoute.value.query.id
})
const loadBusiness = () =>{
  request.get('/business/selectById/' + data.businessId).then(res =>{
    if(res.code === '200'){
      data.businessData = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadBusiness()
const loadType = () =>{
  request.get('/type/selectAll',{
   params:{
     businessId :data.businessId
   }
  }).then(res =>{
    if(res.code === '200'){
      data.typeData = res.data
    }else{
      ElMessage.error(res.msg)
    }
  })
}
loadType()
const navTo = (url) =>{
  location.href = url
}
</script>