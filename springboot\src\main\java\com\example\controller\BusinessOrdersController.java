package com.example.controller;

import com.example.common.Result;
import com.example.entity.BusinessOrders;
import com.example.service.BusinessOrdersService;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 前端请求接口
 */
@RestController
@RequestMapping("/businessOrders")
public class BusinessOrdersController {

    @Resource
    private BusinessOrdersService businessOrdersService;

    /**
     * 新增
     */
    @PostMapping("/add")
    public Result add(@RequestBody BusinessOrders businessOrders) {
        businessOrdersService.add(businessOrders);
        return Result.success();
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    public Result update(@RequestBody BusinessOrders businessOrders) {
        businessOrdersService.updateById(businessOrders);
        return Result.success();
    }
    @PutMapping("/comment")
    public Result comment(@RequestBody BusinessOrders businessOrders) {
        businessOrdersService.comment(businessOrders);
        return Result.success();
    }
    /**
     * 取消
     */
    @PutMapping("/cancel")
    public Result cancel(@RequestBody BusinessOrders businessOrders) {
        businessOrdersService.cancel(businessOrders);
        return Result.success();
    }
    /**
     * 支付
     */
    @PutMapping("/pay")
    public Result pay(@RequestBody BusinessOrders businessOrders) {
        businessOrdersService.pay(businessOrders);
        return Result.success();
    }

    /**
     * 单个删除
     */
    @DeleteMapping("/delete/{id}")
    public Result delete(@PathVariable Integer id) {
        businessOrdersService.deleteById(id);
        return Result.success();
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/delete/batch")
    public Result delete(@RequestBody List<Integer> ids) {
        businessOrdersService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 单个查询
     */
    @GetMapping("/selectById/{id}")
    public Result selectById(@PathVariable Integer id) {
        BusinessOrders businessOrders = businessOrdersService.selectById(id);
        return Result.success(businessOrders);
    }

    /**
     * 查询所有
     */
    @GetMapping("/selectAll")
    public Result selectAll(BusinessOrders businessOrders) {
        List<BusinessOrders> list = businessOrdersService.selectAll(businessOrders);
        return Result.success(list);
    }

    /**
     * 分页查询
     */
    @GetMapping("/selectPage")
    public Result selectPage(BusinessOrders businessOrders,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<BusinessOrders> pageInfo = businessOrdersService.selectPage(businessOrders, pageNum, pageSize);
        return Result.success(pageInfo);
    }

}
