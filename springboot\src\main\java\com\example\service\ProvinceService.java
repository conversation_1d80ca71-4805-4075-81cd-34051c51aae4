package com.example.service;

import cn.hutool.core.date.DateUtil;
import com.example.entity.Province;
import com.example.mapper.ProvinceMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层方法
 */
@Service
public class ProvinceService {

    @Resource
    private ProvinceMapper provinceMapper;

    public void add(Province province) {
        provinceMapper.insert(province);
    }

    public void updateById(Province province) {
        provinceMapper.updateById(province);
    }

    public void deleteById(Integer id) {
        provinceMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            provinceMapper.deleteById(id);
        }
    }

    public Province selectById(Integer id) {
        return provinceMapper.selectById(id);
    }

    public List<Province> selectAll(Province province) {
        return provinceMapper.selectAll(province);
    }

    public PageInfo<Province> selectPage(Province province, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Province> list = provinceMapper.selectAll(province);
        return PageInfo.of(list);
    }

}
