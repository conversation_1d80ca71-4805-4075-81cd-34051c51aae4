<template>
  <div>
    <div class="card" style="margin-bottom: 5px">
      <el-input v-model="data.name" prefix-icon="Search" style="width: 240px; margin-right: 10px" placeholder="请输入名称查询"></el-input>
      <el-button type="info" plain @click="load">查询</el-button>
      <el-button type="warning" plain style="margin: 0 10px" @click="reset">重置</el-button>
    </div>
    <div class="card" style="margin-bottom: 5px">
      <el-button type="primary" plain @click="handleAdd">新增</el-button>
      <el-button type="danger" plain @click="delBatch">批量删除</el-button>
    </div>

    <div class="card" style="margin-bottom: 5px">
      <el-table stripe :data="data.tableData" @selection-change="handleSelectionChange" tooltip-effect="dark myEffect">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="avatar" label="头像">
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 50%; display: block" v-if="scope.row.avatar"
                      :src="scope.row.avatar" :preview-src-list="[scope.row.avatar]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="img" label="宾馆照片">
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 5px; display: block" v-if="scope.row.img"
                      :src="scope.row.img" :preview-src-list="[scope.row.img]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="账号" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="role" label="角色" />
        <el-table-column prop="phone" label="电话" />
        <el-table-column prop="email" label="邮箱" show-overflow-tooltip />
        <el-table-column prop="provinceName" label="所属省份" />
        <el-table-column prop="address" label="地址" show-overflow-tooltip />
        <el-table-column prop="content" label="描述" show-overflow-tooltip/>
        <el-table-column prop="price" label="起始价格">
          <template v-slot="scope">
            <span style="color: red">￥{{ scope.row.price }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="link" label="官网" show-overflow-tooltip />
        <el-table-column prop="license" label="许可证">
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 5px; display: block" v-if="scope.row.license"
                      :src="scope.row.license" :preview-src-list="[scope.row.license]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="leader" label="负责人"  />
        <el-table-column prop="code" label="身份证号" show-overflow-tooltip />
        <el-table-column prop="front" label="身份证正面">
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 5px; display: block" v-if="scope.row.front"
                      :src="scope.row.front" :preview-src-list="[scope.row.front]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="back" label="身份证反面">
          <template v-slot="scope">
            <el-image style="width: 40px; height: 40px; border-radius: 5px; display: block" v-if="scope.row.back"
                      :src="scope.row.back" :preview-src-list="[scope.row.back]" preview-teleported></el-image>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" fixed="right">
          <template v-slot="scope">
            <el-tag type="warning" v-if="scope.row.status === '待审核'">{{ scope.row.status }}</el-tag>
            <el-tag type="success" v-if="scope.row.status === '通过'">{{ scope.row.status }}</el-tag>
            <el-tag type="danger" v-if="scope.row.status === '拒绝'">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template v-slot="scope">
            <el-button type="primary" circle :icon="Edit" @click="handleEdit(scope.row)"></el-button>
            <el-button type="danger" circle :icon="Delete" @click="del(scope.row.id)"></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="card" v-if="data.total">
      <el-pagination @current-change="load" background layout="prev, pager, next" :page-size="data.pageSize" v-model:current-page="data.pageNum" :total="data.total" />
    </div>

    <el-dialog title="商家信息" v-model="data.formVisible" width="40%" destroy-on-close>
      <el-form ref="formRef" :model="data.form" :rules="data.rules" label-width="100px" style="padding: 20px">
        <el-form-item prop="username" label="账户">
          <el-input v-model="data.form.username" placeholder="请输入账户名称"></el-input>
        </el-form-item>
        <el-form-item prop="name" label="宾馆">
          <el-input v-model="data.form.name" placeholder="请输入宾馆名称"></el-input>
        </el-form-item>
        <el-form-item prop="phone" label="宾馆电话">
          <el-input v-model="data.form.phone" placeholder="请输入宾馆电话"></el-input>
        </el-form-item>
        <el-form-item prop="email" label="宾馆邮箱">
          <el-input v-model="data.form.email" placeholder="请输入宾馆邮箱"></el-input>
        </el-form-item>
        <el-form-item prop="provinceId" label="所属省份">
            <el-select v-model="data.form.provinceId" placeholder="请选择省份" style="width: 100%">
              <el-option
                  v-for="item in data.provinceData"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              />
            </el-select>
        </el-form-item>
        <el-form-item prop="address" label="地址">
          <el-input  type="textarea" :rows="4" v-model="data.form.address" placeholder="请输入宾馆地址"></el-input>
        </el-form-item>
        <el-form-item prop="content" label="描述">
          <el-input  type="textarea" :rows="5" v-model="data.form.content" placeholder="请输入宾馆描述"></el-input>
        </el-form-item>
        <el-form-item prop="price" label="价格">
          <el-input-number v-model="data.form.price" :min="100" />
        </el-form-item>
        <el-form-item prop="img" label="宾馆图片">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleImgUpload"
              list-type="picture"
          >
            <el-button type="primary">上传宾馆图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="license" label="营业执照">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleLicenseUpload"
              list-type="picture"
          >
            <el-button type="primary">上传营业执照</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="leader" label="负责人姓名">
          <el-input   v-model="data.form.leader" placeholder="请输入负责人姓名"></el-input>
        </el-form-item>
        <el-form-item prop="code" label="身份证号">
          <el-input   v-model="data.form.code" placeholder="请输入负责人身份证号"></el-input>
        </el-form-item>
        <el-form-item prop="front" label="身份证正面">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleFrontUpload"
              list-type="picture"
          >
            <el-button type="danger">上传身份证正面照片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="back" label="身份证反面">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleBackUpload"
              list-type="picture"
          >
            <el-button type="danger">上传身份证反面照片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="link" label="官网链接">
          <el-input v-model="data.form.link" placeholder="请输入官网链接"></el-input>
        </el-form-item>
        <el-form-item prop="avatar" label="头像">
          <el-upload
              :action="baseUrl + '/files/upload'"
              :on-success="handleFileUpload"
              list-type="picture"
          >
            <el-button type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item prop="status" label="宾馆状态">
          <el-radio-group v-model="data.form.status" fill="#A3A6AD" >
            <el-radio-button label="通过" value="通过" />
            <el-radio-button label="拒绝" value="拒绝" />
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="data.formVisible = false">取 消</el-button>
          <el-button type="primary" @click="save">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>

import {reactive,ref} from "vue";
import request from "@/utils/request.js";
import {ElMessage, ElMessageBox} from "element-plus";
import {Delete, Edit} from "@element-plus/icons-vue";

const baseUrl = import.meta.env.VITE_BASE_URL

const formRef = ref()

const data = reactive({
  formVisible: false,
  form: {},
  tableData: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  name: null,
  provinceData:[],
  ids: [],
  rules: {
    username: [
      { required: true, message: '请输入账号', trigger: 'blur' },
    ],
    name: [
      { required: true, message: '请输入宾馆名称', trigger: 'blur' },
    ],
    phone: [
      { required: true, message: '请输入宾馆电话', trigger: 'blur' },
    ],
    email: [
      { required: true, message: '请输入宾馆邮箱', trigger: 'blur' },
    ],
    provinceId: [
      { required: true, message: '请选择宾馆所在省份', trigger: 'blur' },
    ],
    address: [
      { required:true, message: '请输入详细地址', trigger: 'blur' },
    ],
    content: [
      { required: true, message: '请输入宾馆描述', trigger: 'blur' },
    ],
    price: [
      { required: true, message: '请输入起始价格', trigger: 'blur' },
    ],
    img: [
      { required: true, message: '请上传民宿图片', trigger: 'blur' },
    ],
    license: [
      { required: true, message: '请上传经营许可证', trigger: 'blur' },
    ],
    front: [
      { required: true, message: '请上传身份证正面', trigger: 'blur' },
    ],
    back: [
      { required: true, message: '请上传身份证反面', trigger: 'blur' },
    ],
    leader: [
      { required: true, message: '请输入负责人姓名', trigger: 'blur' },
    ],
    code: [
      { required: true, message: '请输入负责人身份证号', trigger: 'blur' },
    ],
    status: [
      { required: true, message: '请选择民宿状态', trigger: 'blur' },
    ],
  }
})
const loadProvince = () =>{
  request.get('province/selectAll').then(res => {
    if(res.code === '200'){
      data.provinceData = res.data
    } else{
      ElMessage.error(res.msg)
    }
  })
}
loadProvince()
const load = () => {
  request.get('/business/selectPage', {
    params: {
      pageNum: data.pageNum,
      pageSize: data.pageSize,
      name: data.name
    }
  }).then(res => {
    if (res.code === '200') {
      data.tableData = res.data?.list || []
      data.total = res.data?.total
    }
  })
}
const handleAdd = () => {
  data.form = {}
  data.formVisible = true
}
const handleEdit = (row) => {
  data.form = JSON.parse(JSON.stringify(row))
  data.formVisible = true
}
const add = () => {
  request.post('/business/add', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const update = () => {
  request.put('/business/update', data.form).then(res => {
    if (res.code === '200') {
      ElMessage.success('操作成功')
      data.formVisible = false
      load()
    }
  })
}

const save = () => {
  data.form.id ? update() : add()
}

const del = (id) => {
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete('/business/delete/' + id).then(res => {
      if (res.code === '200') {
        ElMessage.success("删除成功")
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const delBatch = () => {
  if (!data.ids.length) {
    ElMessage.warning("请选择数据")
    return
  }
  ElMessageBox.confirm('删除后数据无法恢复，您确定删除吗？', '删除确认', { type: 'warning' }).then(res => {
    request.delete("/business/delete/batch", {data: data.ids}).then(res => {
      if (res.code === '200') {
        ElMessage.success('操作成功')
        load()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }).catch(err => {
    console.error(err)
  })
}
const handleSelectionChange = (rows) => {
  data.ids = rows.map(v => v.id)
}

const handleFileUpload = (res) => {
  data.form.avatar = res.data
}
const handleImgUpload = (res) => {
  data.form.img = res.data
}
const handleLicenseUpload = (res) => {
  data.form.license = res.data
}
const handleFrontUpload = (res) => {
  data.form.front = res.data
}
const handleBackUpload = (res) => {
  data.form.back = res.data
}
const reset = () => {
  data.name = null
  load()
}

load()
</script>
<style>
.myEffect{
  width:40%
}
</style>