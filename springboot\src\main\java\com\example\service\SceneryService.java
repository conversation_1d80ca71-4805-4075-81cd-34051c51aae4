package com.example.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.example.entity.Province;
import com.example.entity.Scenery;
import com.example.mapper.ProvinceMapper;
import com.example.mapper.SceneryMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务层方法
 */
@Service
public class SceneryService {

    @Resource
    private SceneryMapper sceneryMapper;
    @Resource
    private ProvinceMapper provinceMapper;

    public void add(Scenery scenery) {
        sceneryMapper.insert(scenery);
    }

    public void updateById(Scenery scenery) {
        sceneryMapper.updateById(scenery);
    }

    public void deleteById(Integer id) {
        sceneryMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            sceneryMapper.deleteById(id);
        }
    }

    public Scenery selectById(Integer id) {

         Scenery scenery = sceneryMapper.selectById(id);
         Province province = provinceMapper.selectById(scenery.getProvinceId());
        if (ObjectUtil.isNotEmpty(province)){
            scenery.setProvinceName(province.getName());
        }
        return scenery;
    }

    public List<Scenery> selectAll(Scenery scenery) {
        return sceneryMapper.selectAll(scenery);
    }

    public PageInfo<Scenery> selectPage(Scenery scenery, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Scenery> list = sceneryMapper.selectAll(scenery);
        return PageInfo.of(list);
    }

}
