<template>
  <div  style="width:50%;margin:20px auto;min-height:600px">
    <div class="card">
    <div style="padding:20px 30px;font-size:18px;font-weight:bold">系统公告</div>
    <el-timeline style="max-width: 600px">
      <el-timeline-item :center :timestamp="item.time" placement="top" color="#0bbd87" v-for="item in data.noticeData">
        <h4>{{item.title}}</h4>
        <p>{{item.content}}</p>
      </el-timeline-item>
    </el-timeline>
  </div>
  </div>
</template>
<script setup>
import {reactive} from "vue";
import request from "@/utils/request.js";
import {ElMessage} from "element-plus";
const data = reactive({
  noticeData: []
})

const loadNotice = () =>{
  request.get('/notice/selectAll').then(res =>{
    if(res.code ==='200'){
      data.noticeData = res.data
      if(data.noticeData.length > 3){
        data.noticeData = data.noticeData.slice(0,3)
      }
    } else{
      ElMessage.error(res.msg)
    }
  })
}
loadNotice()
</script>