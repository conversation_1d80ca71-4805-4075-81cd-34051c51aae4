package com.example.mapper;

import com.example.entity.BusinessOrders;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface BusinessOrdersMapper {

    int insert(BusinessOrders businessOrders);

    void updateById(BusinessOrders businessOrders);

    void deleteById(Integer id);

    @Select("select * from `business_orders` where id = #{id}")
    BusinessOrders selectById(Integer id);

    List<BusinessOrders> selectAll(BusinessOrders businessOrders);
    @Select("select * from business_orders where order_no = #{orderNo}")
    BusinessOrders selectByOrderNo(String orderNo);
}
