package com.example.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.example.common.enums.RoleEnum;
import com.example.entity.Account;
import com.example.entity.Product;
import com.example.entity.ProductOrders;
import com.example.entity.User;
import com.example.exception.CustomException;
import com.example.mapper.ProductMapper;
import com.example.mapper.ProductOrdersMapper;
import com.example.mapper.UserMapper;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 业务层方法
 */
@Service
public class ProductOrdersService {

    @Resource
    private ProductOrdersMapper productOrdersMapper;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private UserMapper userMapper;

    //下单
    public void add(ProductOrders productOrders) {
        Product product = productMapper.selectById(productOrders.getProductId());
        if(ObjectUtil.isEmpty(product)) {
            throw new CustomException("500","该特产已经不存在");
        }
        if(productOrders.getNum() > product.getNum()){
            throw new CustomException("500","特产剩余数量不足" +productOrders.getNum() +product.getUnit() + "了");
        }
        Account currentUser = TokenUtils.getCurrentUser();
        productOrders.setUserId(currentUser.getId());
        //设置订单编号
        productOrders.setOrderNo(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        productOrders.setTime(DateUtil.now());
        productOrders.setStatus("待支付");
        //查询特产单价逻辑
        productOrders.setPrice(productOrders.getNum() * product.getPrice());
        productOrdersMapper.insert(productOrders);
        //扣除特产剩余数量
        product.setNum(product.getNum() - productOrders.getNum());
        productMapper.updateById(product);
    }

    public void updateById(ProductOrders productOrders) {
        productOrdersMapper.updateById(productOrders);
    }

    public void deleteById(Integer id) {
        productOrdersMapper.deleteById(id);
    }

    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            productOrdersMapper.deleteById(id);
        }
    }

    public ProductOrders selectById(Integer id) {
        return productOrdersMapper.selectById(id);
    }

    public List<ProductOrders> selectAll(ProductOrders productOrders) {
        return productOrdersMapper.selectAll(productOrders);
    }

    public PageInfo<ProductOrders> selectPage(ProductOrders productOrders, Integer pageNum, Integer pageSize) {
        Account currentUser = TokenUtils.getCurrentUser();
        if(RoleEnum.USER.name().equals(currentUser.getRole()))
        {
            productOrders.setUserId(currentUser.getId());
        }
        PageHelper.startPage(pageNum, pageSize);
        List<ProductOrders> list = productOrdersMapper.selectAll(productOrders);
        return PageInfo.of(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public void pay(ProductOrders productOrders) {
        //在用户账户扣除购买的金额
        Integer userId = productOrders.getUserId();
        User user =userMapper.selectById(userId);
        if (user.getAccount() < productOrders.getPrice()){
            throw new CustomException("500","您的余额不足，请先充值");
        }
        user.setAccount(user.getAccount() - productOrders.getPrice());
        userMapper.updateById(user);
        //增加支付时间以及支付编号
        productOrders.setPayNo(UUID.fastUUID().toString());
        productOrders.setPayTime(DateUtil.now());
        productOrders.setStatus("待发货");
        productOrdersMapper.updateById(productOrders);
    }

    public void cancel(ProductOrders productOrders) {
        //返回特产剩余数量
        Product product = productMapper.selectById(productOrders.getProductId());
        if(ObjectUtil.isNotEmpty(product)){
            product.setNum(product.getNum() + productOrders.getNum());
            productMapper.updateById(product);
        }
        //更新订单状态
        productOrders.setStatus("已取消");
        productOrdersMapper.updateById(productOrders);
    }
}
