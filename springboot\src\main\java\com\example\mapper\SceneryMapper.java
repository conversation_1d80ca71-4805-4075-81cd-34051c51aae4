package com.example.mapper;

import com.example.entity.Scenery;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SceneryMapper {

    int insert(Scenery scenery);

    void updateById(Scenery scenery);

    void deleteById(Integer id);

    @Select("select * from `scenery` where id = #{id}")
    Scenery selectById(Integer id);

    List<Scenery> selectAll(Scenery scenery);

}
